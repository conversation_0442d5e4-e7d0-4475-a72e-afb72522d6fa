<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover"
      name="viewport"
    />
    <link
      type="image/x-icon"
      rel="icon"
      href="https://msstest.sankuai.com/aigc-public-resources/avatar/8c681160-45e8-4232-a3dc-f8e49fe120c9_1756190055767.png"
    />
    <!-- channelIdentifier 替换为您的上报通道标识 -->
    <meta name="lx:category" content="smartassistant" />
    <link rel="dns-prefetch" href="//lx.meituan.net" />
    <link rel="dns-prefetch" href="//lx1.meituan.net" />
    <link rel="dns-prefetch" href="//plx.meituan.com" />
    <title>老董私人管家</title>
    <!-- Owl预采集模块 -->
    <script>
      'use strict';
      !(function (u, d) {
        var t = 'owl',
          e = '_Owl_',
          n = 'Owl',
          r = 'start',
          c = 'error',
          p = 'on' + c,
          f = u[p],
          h = 'addEventListener',
          l = 'attachEvent',
          v = 'isReady',
          b = 'dataSet';
        (u[t] =
          u[t] ||
          function () {
            try {
              u[t].q = u[t].q || [];
              var e = [].slice.call(arguments);
              e[0] === r ? (u[n] && u[n][r] ? u[n][r](e[1]) : u[t].q.unshift(e)) : u[t].q.push(e);
            } catch (e) {}
          }),
          (u[e] = u[e] || {
            preTasks: [],
            pageData: [],
            use: function (e, t) {
              this[v] ? u[n][e](t) : this.preTasks.push({ api: e, data: [t] });
            },
            run: function (t) {
              if (!(t = this).runned) {
                (t.runned = !0),
                  (t[b] = []),
                  (u[p] = function () {
                    t[v] || t[b].push({ type: 'jsError', data: arguments }),
                      f && f.apply(u, arguments);
                  }),
                  u[h] &&
                    u[h]('unhandledrejection', function (e) {
                      t[v] || t[b].push({ type: 'jsError', data: [e] });
                    });
                var e = function (e) {
                  !t[v] && e && t[b].push({ type: 'resError', data: [e] });
                };
                u[h] ? u[h](c, e, !0) : u[l] && u[l](p, e);
                var n = 'MutationObserver',
                  r = u[n] || u['WebKit' + n] || u['Moz' + n],
                  a = u.performance || u.WebKitPerformance,
                  s = 'disableMutaObserver';
                if (r && a && a.now)
                  try {
                    var i = -1,
                      o = u.navigator.userAgent;
                    -1 < o.indexOf('compatible') && -1 < o.indexOf('MSIE')
                      ? (new RegExp('MSIE (\\d+\\.\\d+);').test(o), (i = parseFloat(RegExp.$1)))
                      : -1 < o.indexOf('Trident') && -1 < o.indexOf('rv:11.0') && (i = 11),
                      -1 !== i && i <= 11
                        ? (t[s] = !0)
                        : (t.observer = new r(function (e) {
                            t.pageData.push({ mutations: e, startTime: a.now() });
                          })).observe(d, { childList: !0, subtree: !0 });
                  } catch (e) {}
                else t[s] = !0;
              }
            },
          }),
          u[e].runned || u[e].run();
      })(window, document);
    </script>
  </head>
  <script>
    (function (doc, win) {
      var docEl = doc.documentElement,
        resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',
        recalc = function () {
          // 根据浏览器高度计算应用宽度
          var heightToWidthRatio = 0.55; // 宽度为高度的55%
          var minAppWidth = 320; // 最小宽度限制（像素）
          var minAppHeight = 600; // 最小高度限制（像素）
          var smallScreenThreshold = 500; // 小屏幕阈值（像素）

          // 获取浏览器尺寸
          var windowWidth = window.innerWidth;
          var windowHeight = Math.max(window.innerHeight, minAppHeight);

          // 计算基于高度的理想宽度
          var idealWidth = windowHeight * heightToWidthRatio;
          idealWidth = Math.max(minAppWidth, idealWidth);

          // 判断是否需要铺满屏幕
          var isSmallScreen = windowWidth <= smallScreenThreshold;
          var isNarrowScreen = windowWidth < idealWidth;
          var shouldFillScreen = isSmallScreen || isNarrowScreen;

          // 动态检查当前路由是否需要全屏（只有ThreeInOne页面需要全屏）
          var isThreeInOnePage = window.location.pathname.includes('three-in-one');
          if (isThreeInOnePage) {
            shouldFillScreen = true;
          }

          // 计算应用宽度
          var calculatedWidth;

          if (shouldFillScreen || isThreeInOnePage) {
            // 小屏幕、窄屏幕或ThreeInOne页面下铺满浏览器宽度
            calculatedWidth = windowWidth;
          } else {
            // 正常屏幕下使用基于高度计算的宽度
            calculatedWidth = idealWidth;
          }

          // 添加全局样式使应用保持基于高度的宽度
          var styleElement = doc.getElementById('mobile-style') || doc.createElement('style');
          styleElement.id = 'mobile-style';
          styleElement.innerHTML = `
                body {
                    background-color: #f8f8f8 !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    overflow-x: hidden !important;
                    display: flex !important;
                    justify-content: center !important;
                }
                #app {
                    width: ${calculatedWidth}px !important;
                    ${shouldFillScreen ? '' : `min-width: ${minAppWidth}px !important;`}
                    margin: 0 auto !important;
                    height: 100vh !important;
                    min-height: ${minAppHeight}px !important;
                    position: relative !important;
                    box-shadow: ${shouldFillScreen ? 'none' : '0 0 20px rgba(0, 0, 0, 0.1)'} !important;
                    overflow: hidden !important;
                    border-radius: 0 !important;
                }
            `;
          if (!doc.getElementById('mobile-style')) {
            doc.head.appendChild(styleElement);
          }

          // 计算字体大小基于应用实际宽度
          // 对于ThreeInOne页面，限制字体大小计算的基准宽度，避免组件过大
          var fontSizeBaseWidth = calculatedWidth;
          if (isThreeInOnePage) {
            // 进一步减小组件大小，使用更小的基准宽度
            fontSizeBaseWidth = Math.min(calculatedWidth, 450);
          }
          docEl.style.fontSize = 10 * (fontSizeBaseWidth / 750) + 'px';
        };

      if (!doc.addEventListener) return;
      win.addEventListener(resizeEvt, recalc, false);
      doc.addEventListener('DOMContentLoaded', recalc, false);

      // 监听路由变化（用于SPA应用）
      var originalPushState = history.pushState;
      var originalReplaceState = history.replaceState;

      history.pushState = function() {
        originalPushState.apply(history, arguments);
        setTimeout(recalc, 0); // 异步执行，确保路由已经更新
      };

      history.replaceState = function() {
        originalReplaceState.apply(history, arguments);
        setTimeout(recalc, 0);
      };

      // 监听浏览器前进后退
      win.addEventListener('popstate', function() {
        setTimeout(recalc, 0);
      }, false);
    })(document, window);
  </script>
  <body>
    <div id="app"></div>
    <!-- owl js文件 -->
    <script
      crossorigin="anonymous"
      src="https://s3.meituan.net/mnpm-cdn/@mtfe-mt-apm-web-1.12.1/owl_1.12.1.min.js"
    ></script>
    <script>
      // 灵犀种子代码开始（以下这部分不能动）
      !(function (win, doc, ns) {
        win['_MeiTuanALogObject'] = ns;
        if (!win[ns]) {
          var _LX = function () {
            var t = function () {
              var inst = function () {
                inst.q.push([arguments, +new Date()]);
              };
              inst.q = [];
              t.q.push([arguments, inst]);
              return inst;
            };
            t.q = [];
            t.t = +new Date();
            _LX.q.push([arguments, t]);
            return t;
          };
          _LX.q = _LX.q || [];
          _LX.l = +new Date();
          win[ns] = _LX;
        }
      })(window, document, 'LXAnalytics');
      // 灵犀种子代码结束（以上这部分不能动)
    </script>

    <script type="text/javascript" src="//lx.meituan.net/lx.5.min.js" async></script>
    <script>
      // 灵犀初始化配置
      LXAnalytics('config', {
        appName: 'fe-ai-speech', //页面应用名
        autoTrack: true, //是否开启部分事件自动埋点，预计在二期实现
        isSPA: true, //是否是单页面应用
        mvDelay: 0, //合并mv事件的缓存秒数，0为关闭
        onWebviewAppearAutoPV: true, //在app内嵌页时，容器显示/隐藏时的自动PV/PD开关
        onVisibilityChangeAutoPV: true, //在pc端，切换tab页签时的自动PV/PD开关
        onWindowFocusAutoPV: true, //在pc端，当window获得/失去焦点时的自动PV/PD开关
        onVCGap: 2, //pc端切换tab、window失焦时，间隔多久切回来才会触发自动PV/PD。最小有效值2，单位秒
        sessionScope: 'top', //session种在一级域下还是当前域下，默认top为一级域，sub为当前域
        nativeReport: 'on', //是否开启app内嵌页代报
        defaultCategory: 'smartassistant',
        // isDev: "<%= process.env.DEPLOY_ENV %>" !== 'production' // 全部上报到线上环境，产运自己根据业务参数fe_ai_speech_env来区环境查看数据
      });
    </script>
    <script src="https://s3plus.meituan.net/v1/mss_28a77f134e5b4abf876b4ff035f4107f/iconfont/project/1130/0.0.3/llm-factory-h5.js"></script>
    <script crossorigin="anonymous" src="//www.dpfile.com/app/owl/static/owl_1.10.1.js"></script>
  </body>
</html>
