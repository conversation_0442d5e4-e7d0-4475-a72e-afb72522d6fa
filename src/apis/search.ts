import { fetchEventSource } from '@microsoft/fetch-event-source';
import { getAccessToken } from '@/lib/fetch';

// 支持的模型列表
export const SUPPORTED_MODELS = [
  'anthropic.claude-sonnet-4',
  'LongCat-Flash-Chat-Preview',
  'kimi-k2-0711-preview',
  'deepseek-v3-friday'
] as const;

// 模型类型
export type SupportedModel = typeof SUPPORTED_MODELS[number];

// 综合搜索请求接口
export interface IComprehensiveSearchRequest {
  question: string;
  user_id: string;
  model?: SupportedModel; // 可选的模型字段，保持向后兼容
}

// 搜索状态响应接口
export interface ISearchStatusResponse {
  type: 'status';
  data: {
    step: 'start' | 'processing' | 'complete';
    message: string;
    elapsed_time: number;
  };
  timestamp: number;
}

// 问题响应接口
export interface ISearchQuestionResponse {
  type: 'question';
  data: {
    question: string;
    user_id: string;
    elapsed_time: number;
  };
  timestamp: number;
}

// 日志响应接口
export interface ISearchLogResponse {
  type: 'log';
  data: {
    message: string;
    elapsed_time: number;
  };
  timestamp: number;
}

// 知识上下文响应接口
export interface ISearchKnowledgeContextResponse {
  type: 'knowledge_context';
  data: Record<string, unknown>;
  timestamp: number;
}

// 搜索问题响应接口
export interface ISearchQuestionsResponse {
  type: 'search_questions';
  data: {
    questions: string[];
    elapsed_time: number;
  };
  timestamp: number;
}

// 搜索结果响应接口
export interface ISearchResultResponse {
  type: 'search_result';
  data: {
    question: string;
    results: Array<{
      snippet: string;
      title: string;
      link: string;
      images: string[];
      crawled_content: string;
      content_summary: string;
    }>;
    elapsed_time: number;
  };
  timestamp: number;
}

// 核心答案开始响应接口
export interface ICoreAnswerStartResponse {
  type: 'core_answer_start';
  data: {
    answer_type: string;
    total_length: number;
    elapsed_time: number;
  };
  timestamp: number;
}

// 最终答案响应接口
export interface IFinalAnswerResponse {
  type: 'final_answer_complete';
  data: {
    core_answer: string;
    detailed_answer: string;
    final_answer: string;
    total_time: number;
    elapsed_time: number;
  };
  timestamp: number;
}

// 综合搜索响应类型联合
export type IComprehensiveSearchResponse =
  | ISearchStatusResponse
  | ISearchQuestionResponse
  | ISearchLogResponse
  | ISearchKnowledgeContextResponse
  | ISearchQuestionsResponse
  | ISearchResultResponse
  | ICoreAnswerStartResponse
  | IFinalAnswerResponse;

// 综合搜索流式接口
export const comprehensiveSearchStream = (
  params: IComprehensiveSearchRequest,
  callbacks: {
    onStatus: (data: ISearchStatusResponse['data']) => void;
    onQuestion: (data: ISearchQuestionResponse['data']) => void;
    onLog: (data: ISearchLogResponse['data']) => void;
    onKnowledgeContext: (data: ISearchKnowledgeContextResponse['data']) => void;
    onSearchQuestions: (data: ISearchQuestionsResponse['data']) => void;
    onSearchResult: (data: ISearchResultResponse['data']) => void;
    onCoreAnswerStart: (data: ICoreAnswerStartResponse['data']) => void;
    onFinalAnswer: (data: IFinalAnswerResponse['data']) => void;
    onError: (error: Error) => void;
    onClose?: () => void;
  },
  signal?: AbortSignal,
) => {
  const url = '/humanrelation/comprehensive_search_stream';

  return fetchEventSource(url, {
    method: 'POST',
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Content-Type': 'application/json',
      Accept: 'text/event-stream',
      'x-requested-with': 'XMLHttpRequest',
      'X-Accel-Buffering': 'no',
      'X-Unbuffered': 'true',
      'access-token': getAccessToken() || '',
    },
    body: JSON.stringify(params),
    signal,
    onmessage(event) {
      try {
        const data = JSON.parse(event.data) as IComprehensiveSearchResponse;
        console.log('🔍 [comprehensiveSearchStream] 解析的数据:', data);

        switch (data.type) {
          case 'status':
            console.log('📊 [comprehensiveSearchStream] 状态更新:', data.data);
            callbacks.onStatus(data.data);
            break;
          case 'question':
            console.log('❓ [comprehensiveSearchStream] 问题:', data.data);
            callbacks.onQuestion(data.data);
            break;
          case 'log':
            console.log('📝 [comprehensiveSearchStream] 日志:', data.data);
            callbacks.onLog(data.data);
            break;
          case 'knowledge_context':
            console.log('📚 [comprehensiveSearchStream] 知识上下文:', data.data);
            callbacks.onKnowledgeContext(data.data);
            break;
          case 'search_questions':
            console.log('🔍 [comprehensiveSearchStream] 搜索问题:', data.data);
            callbacks.onSearchQuestions(data.data);
            break;
          case 'search_result':
            console.log('📋 [comprehensiveSearchStream] 搜索结果:', data.data);
            callbacks.onSearchResult(data.data);
            break;
          case 'core_answer_start':
            console.log('🚀 [comprehensiveSearchStream] 核心答案开始:', data.data);
            callbacks.onCoreAnswerStart(data.data);
            break;
          case 'final_answer_complete':
            console.log('✅ [comprehensiveSearchStream] 最终答案:', data.data);
            callbacks.onFinalAnswer(data.data);
            break;
          default:
            console.warn('⚠️ [comprehensiveSearchStream] 未知响应类型:', data);
        }
      } catch (error) {
        console.error('解析SSE消息失败:', error);
        callbacks.onError(error instanceof Error ? error : new Error(String(error)));
      }
    },
    onerror(error) {
      console.error('SSE连接错误:', error);

      // 如果是 AbortError，说明是用户主动取消，不需要当作错误处理
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('SSE连接被用户主动取消');
        return;
      }

      callbacks.onError(error instanceof Error ? error : new Error(String(error)));
      throw error; // 重新抛出错误以停止重连
    },
    onclose() {
      console.log('SSE连接已关闭');
      // 调用可选的 onClose 回调
      if (callbacks.onClose) {
        callbacks.onClose();
      }
    },
  });
};
