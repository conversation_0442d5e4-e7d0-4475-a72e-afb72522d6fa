import { ref, onUnmounted } from 'vue';
import type { Ref } from 'vue';
import { getStreamSynthesis, getTtsResponse } from '@/apis/chat';

/**
 * 音频队列状态接口
 */
export type IAudioType = 'manualPlay' | 'autoPlay';
export type AudioStatus = 'idle' | 'loading' | 'playing' | 'completed';

interface IAudioQueueState {
  isPlaying: Ref<boolean>;
  currentPlayingData: Ref<ICurrentPlayingData>;
  audioStatus: Ref<AudioStatus>;
  play: (audioQuery: ICurrentPlayingData) => void;
  stop: () => void;
  setVolume: (value: number) => void;
  isCurrentAudioPlaying: (id: string) => boolean;
}
interface ICurrentPlayingData {
  id: string;
  text: string;
  type: IAudioType;
}
// 创建一个全局单例
let instance: IAudioQueueState | null = null;

// 检查是否支持 MediaSource
const isMediaSourceSupported = 'MediaSource' in window && MediaSource.isTypeSupported('audio/mpeg');

// 全局状态管理（移到函数外部）
const isPlaying = ref(false);
const currentPlayingData = ref<ICurrentPlayingData>({} as ICurrentPlayingData);
const volume = ref(1);
const audioStatus = ref<AudioStatus>('idle');

// 全局音频相关变量（移到函数外部）
let audioElement: HTMLAudioElement | null = null;
let mediaSource: MediaSource | null = null;
let sourceBuffer: SourceBuffer | null = null;
let abortController: AbortController | null = null;
let isDestroyed = false;

// 媒体数据队列（移到函数外部）
let mediaQueue: Uint8Array[] = [];

// 组件引用计数，用于管理全局资源的生命周期
let componentRefCount = 0;

/**
 * 音频队列管理 Hook
 */
export function useAudioQueue(): IAudioQueueState {
  // 增加引用计数
  componentRefCount++;

  // 如果之前被销毁了，现在有新组件使用，需要重置状态
  if (isDestroyed && componentRefCount > 0) {
    console.log('🔄 [useAudioPlayer] 重置已销毁的音频播放器状态');
    isDestroyed = false;
  }

  if (instance) {
    // 如果实例已存在，注册当前组件的卸载钩子
    onUnmounted(() => {
      componentRefCount--;
      // 只有当所有组件都卸载时才真正销毁资源
      if (componentRefCount <= 0) {
        console.log('🔄 [useAudioPlayer] 所有组件已卸载，清理全局音频资源');
        isDestroyed = true;
        // 调用实例的stop方法而不是未定义的stop函数
        if (instance && instance.stop) {
          instance.stop();
        }
        // 重置全局状态
        componentRefCount = 0;
        instance = null;
      }
    });
    return instance;
  }

  /**
   * 检查指定ID的音频是否正在播放
   */
  const isCurrentAudioPlaying = (id: string): boolean => {
    return isPlaying.value && currentPlayingData.value.id === id;
  };

  /**
   * 初始化音频上下文
   */
  const initAudioContext = () => {
    return new Promise<void>((resolve) => {
      audioElement = new Audio();
      audioElement.volume = volume.value;
      audioElement.autoplay = true;
      audioElement.crossOrigin = 'anonymous';

      if (isMediaSourceSupported) {
        console.log('使用MediaSource');
        mediaSource = new MediaSource();
        audioElement.src = URL.createObjectURL(mediaSource);

        mediaSource.addEventListener(
          'sourceopen',
          () => {
            if (mediaSource?.sourceBuffers.length === 0) {
              sourceBuffer = mediaSource.addSourceBuffer('audio/mpeg');
            } else {
              sourceBuffer = mediaSource?.sourceBuffers[0] || null;
            }

            sourceBuffer?.addEventListener('updateend', () => {
              if (mediaQueue.length > 0) {
                appendNextMediaChunk();
              }
            });

            resolve();
          },
          { once: true },
        );
      } else {
        resolve();
      }
    });
  };

  /**
   * 追加下一个媒体数据块
   */
  const appendNextMediaChunk = () => {
    if (!sourceBuffer || !mediaQueue.length || sourceBuffer.updating) return;

    const chunk = mediaQueue.shift();
    if (chunk) {
      sourceBuffer.appendBuffer(chunk);
    }
  };

  /**
   * 清理当前音频资源
   */
  const cleanupCurrentAudio = () => {
    try {
      if (audioElement) {
        audioElement.oncanplay = null;
        audioElement.onloadedmetadata = null;
        audioElement.onerror = null;
        audioElement.onended = null;

        audioElement.pause();
        audioElement.removeAttribute('src');
        audioElement.load();
      }

      if (isMediaSourceSupported && mediaSource) {
        if (sourceBuffer && mediaSource.readyState === 'open') {
          try {
            if (!sourceBuffer.updating) {
              mediaSource.removeSourceBuffer(sourceBuffer);
            }
          } catch (e) {
            console.warn('SourceBuffer 移除失败:', e);
          }
        }

        if (mediaSource.readyState === 'open') {
          try {
            mediaSource.endOfStream();
          } catch (e) {
            console.warn('MediaSource 关闭失败:', e);
          }
        }
      }

      mediaQueue = [];
      sourceBuffer = null;
      mediaSource = null;
      audioElement = null;

      isPlaying.value = false;
      currentPlayingData.value = {} as ICurrentPlayingData;
      audioStatus.value = 'idle';
    } catch (error) {
      throw new Error(`清理音频资源时出错: ${error}`);
    }
  };

  /**
   * 播放指定ID的音频
   */
  const play = async (audioQuery: ICurrentPlayingData) => {
    console.log(audioQuery);
    console.log((!audioQuery.id && !audioQuery.text) || isDestroyed);
    if ((!audioQuery.id && !audioQuery.text) || isDestroyed) {
      console.log(isDestroyed);
      return;
    }
    console.log(99988);
    // 如果当前有音频在播放，先停止并清理
    if (isPlaying.value) {
      stop();
    }

    currentPlayingData.value = audioQuery;
    isPlaying.value = true;
    audioStatus.value = 'loading';

    try {
      await initAudioContext();
      await streamAudio(audioQuery);
    } catch (error) {
      cleanupCurrentAudio();
      throw new Error(`播放失败: ${error}`);
    }
  };

  /**
   * 流式获取并播放音频
   */
  const streamAudio = async (audioQuery: {
    id: string;
    text: string;
    type: string;
  }): Promise<void> => {
    if (!audioElement) {
      throw new Error(`音频上下文未正确初始化`);
    }

    abortController = new AbortController();

    try {
      let response;
      if (audioQuery.type === 'autoPlay') {
        response = await getTtsResponse({
          id: audioQuery.id,
          signal: abortController.signal,
        });
      } else {
        response = await getStreamSynthesis({
          text: audioQuery.text,
          signal: abortController.signal,
        });
      }
      if (!response.ok) {
        audioStatus.value = 'idle';
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const contentType = response.headers.get('Content-Type') || '';
      if (!contentType.includes('audio/')) {
        throw new Error(`不支持的内容类型: ${contentType}`);
      }

      audioElement.preload = 'auto';

      // 如果不支持 MediaSource，直接使用 Blob URL 播放
      if (!isMediaSourceSupported) {
        console.log('使用BlobURL');
        const blob = await response.blob();

        // 检查 blob 是否为空或太小
        if (!blob || blob.size === 0) {
          console.error('接收到空的音频数据');
          cleanupCurrentAudio();
          throw new Error('接收到空的音频数据');
        }

        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        const blobUrl = URL.createObjectURL(blob);

        audioElement.src = blobUrl;
        audioElement.onended = () => {
          URL.revokeObjectURL(blobUrl);
          audioStatus.value = 'completed';
          cleanupCurrentAudio();
        };

        // 添加错误处理
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        audioElement.onerror = (e: any) => {
          URL.revokeObjectURL(blobUrl);
          audioStatus.value = 'idle';
          cleanupCurrentAudio();
          throw new Error(`音频加载失败: ${e}`);
        };

        audioElement.onplay = () => {
          audioStatus.value = 'playing';
          isPlaying.value = true;
        };

        try {
          await audioElement.play();
          isPlaying.value = true;
        } catch (error) {
          URL.revokeObjectURL(blobUrl);
          audioStatus.value = 'idle';
          cleanupCurrentAudio();
          throw new Error(`音频加载失败: ${error}`);
        }
        return;
      }

      // MediaSource 流式播放
      if (!sourceBuffer) return;

      const reader = response.body?.getReader();
      if (!reader) throw new Error('无法读取响应流');

      let hasReceivedData = false;

      audioElement.onplay = () => {
        audioStatus.value = 'playing';
        isPlaying.value = true;
      };

      audioElement.onended = () => {
        console.log('音频播放结束');
        audioStatus.value = 'completed';
        cleanupCurrentAudio();
      };

      // eslint-disable-next-line no-constant-condition
      while (true) {
        // eslint-disable-next-line no-await-in-loop
        const { done, value } = await reader.read();

        if (done) {
          console.log('数据流读取完成');
          if (!hasReceivedData) {
            console.error('未接收到任何音频数据');
            cleanupCurrentAudio();
            throw new Error('未接收到任何音频数据');
          }
          break;
        }

        if (value && value.length > 0) {
          hasReceivedData = true;
          if (!sourceBuffer.updating) {
            sourceBuffer.appendBuffer(value as BufferSource);
            if (!isPlaying.value) {
              try {
                const playPromise = audioElement.play();
                if (playPromise !== undefined) {
                  // eslint-disable-next-line no-await-in-loop
                  await playPromise;
                  isPlaying.value = true;
                }
              } catch (playError) {
                throw new Error(`播放尝试失败: ${playError}`);
              }
            }
          } else {
            mediaQueue.push(value as Uint8Array);
          }
        }
      }

      if (mediaSource?.readyState === 'open') {
        setTimeout(() => {
          mediaSource?.endOfStream();
        }, 0);
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      if (error?.name === 'AbortError') {
        return;
      }
      throw error;
    }
  };

  /**
   * 设置音量
   */
  const setVolume = (value: number) => {
    volume.value = Math.max(0, Math.min(1, value));
    if (audioElement) {
      audioElement.volume = volume.value;
    }
  };

  /**
   * 停止播放
   */
  const stop = () => {
    if (abortController) {
      abortController.abort();
      abortController = null;
    }

    cleanupCurrentAudio();
  };

  /**
   * 组件卸载时清理
   */
  onUnmounted(() => {
    componentRefCount--;
    // 只有当所有组件都卸载时才真正销毁资源
    if (componentRefCount <= 0) {
      console.log('🔄 [useAudioPlayer] 所有组件已卸载，清理全局音频资源');
      isDestroyed = true;
      // 直接调用stop函数，此时已经定义了
      stop();
      // 重置全局状态
      componentRefCount = 0;
      instance = null;
    }
  });

  instance = {
    isPlaying,
    currentPlayingData,
    audioStatus,
    play,
    stop,
    setVolume,
    isCurrentAudioPlaying,
  };

  return instance;
}
