/**
 * AgentPK 专用打字机效果
 * 独立实现，不依赖项目其他部分
 */

export interface TypewriterOptions {
  /** 打字速度（毫秒/字符） */
  speed?: number;
  /** 完成回调 */
  onComplete?: () => void;
  /** 内容更新回调 */
  onUpdate?: (content: string) => void;
}

export class AgentTypewriter {
  private content = '';
  private displayedContent = '';
  private currentIndex = 0;
  private isRunning = false;
  private timer: ReturnType<typeof setTimeout> | null = null;
  private speed: number;
  private onComplete?: () => void;
  private onUpdate?: (content: string) => void;

  constructor(options: TypewriterOptions = {}) {
    this.speed = options.speed || 30; // 默认30ms/字符，比原来更快
    this.onComplete = options.onComplete;
    this.onUpdate = options.onUpdate;
  }

  /**
   * 设置要显示的完整内容
   */
  setContent(content: string) {
    this.content = content;
    this.currentIndex = 0;
    this.displayedContent = '';
  }

  /**
   * 开始打字机效果
   */
  start() {
    if (this.isRunning) {
      this.stop();
    }

    this.isRunning = true;
    this.currentIndex = 0;
    this.displayedContent = '';
    this.typeNextChar();
  }

  /**
   * 停止打字机效果
   */
  stop() {
    this.isRunning = false;
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }

  /**
   * 立即完成显示
   */
  complete() {
    this.stop();
    this.displayedContent = this.content;
    this.currentIndex = this.content.length;
    
    if (this.onUpdate) {
      this.onUpdate(this.displayedContent);
    }
    
    if (this.onComplete) {
      this.onComplete();
    }
  }

  /**
   * 获取当前显示的内容
   */
  getCurrentContent(): string {
    return this.displayedContent;
  }

  /**
   * 检查是否正在运行
   */
  isActive(): boolean {
    return this.isRunning;
  }

  /**
   * 检查是否已完成
   */
  isCompleted(): boolean {
    return this.currentIndex >= this.content.length;
  }

  /**
   * 打字下一个字符
   */
  private typeNextChar() {
    if (!this.isRunning || this.currentIndex >= this.content.length) {
      // 完成
      this.isRunning = false;
      if (this.onComplete) {
        this.onComplete();
      }
      return;
    }

    // 添加下一个字符
    this.currentIndex++;
    this.displayedContent = this.content.substring(0, this.currentIndex);

    // 更新显示
    if (this.onUpdate) {
      this.onUpdate(this.displayedContent);
    }

    // 继续下一个字符
    this.timer = setTimeout(() => {
      this.typeNextChar();
    }, this.speed);
  }

  /**
   * 更新打字速度
   */
  setSpeed(speed: number) {
    this.speed = speed;
  }

  /**
   * 销毁打字机，清理资源
   */
  destroy() {
    this.stop();
    this.content = '';
    this.displayedContent = '';
    this.currentIndex = 0;
    this.onComplete = undefined;
    this.onUpdate = undefined;
  }
}
