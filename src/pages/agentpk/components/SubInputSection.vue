<template>
  <div class="sub-input-section">
    <div class="sub-input-container">
      <input
        v-model="inputText"
        type="text"
        class="sub-input-field"
        placeholder="单独提问..."
        @keyup.enter="handleSubmit"
      />
      <button class="sub-submit-btn" @click="handleSubmit" :disabled="isLoading">
        <svg v-if="!isLoading" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
        </svg>
        <div v-else class="loading-spinner"></div>
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineEmits } from 'vue';
import { showFailToast } from 'vant';

const emit = defineEmits(['send-message']);

const inputText = ref('');
const isLoading = ref(false);

const handleSubmit = () => {
  if (!inputText.value.trim()) {
    showFailToast('请输入问题内容');
    return;
  }

  if (isLoading.value) {
    showFailToast('请等待当前回复完成');
    return;
  }

  console.log('🚀 [SubInputSection] 提交问题:', inputText.value);
  const message = inputText.value.trim();
  inputText.value = ''; // 清空输入框
  emit('send-message', message);
};

// 暴露方法给父组件
defineExpose({
  setLoading: (loading: boolean) => {
    isLoading.value = loading;
  },
  clearInput: () => {
    inputText.value = '';
  },
  setInputValue: (value: string) => {
    inputText.value = value;
  },
});
</script>

<style lang="scss" scoped>
.sub-input-section {
  width: 100%;
  padding: 12px 16px;
  border-top: 1px solid rgba(139, 126, 216, 0.2);
  background: rgba(248, 249, 250, 0.5);
}

.sub-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(139, 126, 216, 0.2);
  border-radius: 20px;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: #8B7ED8;
    box-shadow: 0 2px 8px rgba(139, 126, 216, 0.2);
    background: rgba(255, 255, 255, 0.95);
  }
}

.sub-input-field {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 18px;
  color: #333;
  padding: 4px 0;

  &::placeholder {
    color: #999;
    font-size: 18px;
  }
}

.sub-submit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(90deg, #8B7ED8 0%, #B794F6 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;

  &:hover:not(:disabled) {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(139, 126, 216, 0.3);
  }

  &:active:not(:disabled) {
    transform: scale(0.95);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  svg {
    width: 14px;
    height: 14px;
  }
}

.loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
