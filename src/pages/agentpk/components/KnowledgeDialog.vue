<template>
  <div class="knowledge-dialog-overlay">
    <div
      v-if="true"
      class="cyber-enhanced-memo-modal"
      @click="handleClose"
    >
      <div class="cyber-enhanced-memo-modal__overlay" />

      <div
        class="cyber-enhanced-memo-modal__container cyber-enhanced-memo-modal__container--knowledge"
        @click.stop
      >
        <button
          class="cyber-enhanced-memo-modal__close-btn"
          aria-label="关闭"
          @click="handleClose"
        >
          ×
        </button>

        <div class="cyber-enhanced-memo-modal__header">
          <h3 class="cyber-enhanced-memo-modal__title">
            📚 公司知识库
          </h3>
          <div class="cyber-enhanced-memo-modal__divider"></div>
        </div>
        <div class="cyber-enhanced-memo-modal__content">
          <!-- 知识库列表区域 -->
          <div class="knowledge-section">
            <!-- 加载状态 -->
            <div v-if="isLoading" class="loading-state">
              <div class="loading-text">加载中...</div>
            </div>

            <!-- 知识库条目列表 -->
            <div v-else class="knowledge-list">
              <div
                v-for="item in currentPageItems"
                :key="item.id"
                class="knowledge-item"
              >
                <div class="knowledge-term">{{ item.term }}</div>
                <div class="knowledge-definition">{{ item.definition }}</div>
              </div>

              <!-- 空状态 -->
              <div v-if="knowledgeItems.length === 0" class="empty-state">
                <div class="empty-text">暂无知识库内容</div>
              </div>
            </div>

            <!-- 分页控制 -->
            <div v-if="totalPages > 1" class="pagination-controls">
              <button
                class="pagination-btn"
                :disabled="currentPage === 1"
                @click="goToPage(currentPage - 1)"
              >
                上一页
              </button>
              <span class="pagination-info">
                {{ currentPage }} / {{ totalPages }}
              </span>
              <button
                class="pagination-btn"
                :disabled="currentPage === totalPages"
                @click="goToPage(currentPage + 1)"
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import { getKnowledgeList, type IKnowledgeItem } from '@/apis/knowledge';

// Emits定义
const emit = defineEmits<{
  close: [];
}>();

// 处理关闭
const handleClose = () => {
  emit('close');
};

// 响应式数据
const knowledgeItems = ref<IKnowledgeItem[]>([]);
const isLoading = ref(false);
const currentPage = ref(1);
const pageSize = 10; // 每页显示10个条目

// 计算属性
const totalPages = computed(() => {
  return Math.ceil(knowledgeItems.value.length / pageSize);
});

const currentPageItems = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return knowledgeItems.value.slice(startIndex, endIndex);
});

// 获取知识库数据
const fetchKnowledgeItems = async () => {
  try {
    isLoading.value = true;
    console.log('📤 [KnowledgeDialog] 开始获取知识库数据');

    const response = await getKnowledgeList({
      limit: 100,
      offset: 0,
    });

    if (response.result === 'success' && response.knowledge_items) {
      knowledgeItems.value = response.knowledge_items;
      console.log('✅ [KnowledgeDialog] 知识库数据获取成功:', knowledgeItems.value.length, '条');
    } else {
      console.warn('⚠️ [KnowledgeDialog] 知识库数据获取失败或无数据');
      knowledgeItems.value = [];
    }
  } catch (error) {
    console.error('❌ [KnowledgeDialog] 获取知识库数据失败:', error);
    knowledgeItems.value = [];
  } finally {
    isLoading.value = false;
  }
};

// 分页方法
const goToPage = async (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    // 切换页面后，将知识库列表滚动到顶部
    await nextTick();
    const knowledgeList = document.querySelector('.knowledge-list');
    if (knowledgeList) {
      knowledgeList.scrollTop = 0;
    }
  }
};

// 组件挂载时获取数据
onMounted(() => {
  void fetchKnowledgeItems();
});
</script>

<style lang="scss" scoped>
.knowledge-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
  pointer-events: auto;
  padding: 40px 20px;
  box-sizing: border-box;
}

.cyber-enhanced-memo-modal {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;

  &__overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
  }

  &__container {
    position: relative;
    max-width: 1200px;
    width: 98%;
    max-height: 90vh;
    padding: 40px;
    border-radius: 24px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);

    &--knowledge {
      background: #ffffff; // 白色背景
      border: 1px solid #e5e7eb;
    }
  }

  &__close-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: #6b7280;
    font-size: 24px;
    line-height: 1;
    transition: all 0.2s ease;
    border: none;
    background: transparent;

    &:hover {
      color: #374151;
    }
  }

  &__header {
    margin-bottom: 16px;
  }

  &__title {
    margin: 0 0 16px 0;
    font-size: 32px;
    font-weight: 600;
    color: #1f2937;
    text-align: left;
  }

  &__divider {
    width: 100%;
    height: 1px;
    background: #d1d5db;
    margin: 0;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 28px;
  }
}

// 知识库内容区域
.knowledge-section {
  border: 1px solid #d1d5db;
  border-radius: 12px;
  padding: 24px;
  background: #f9fafb;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  min-height: 700px;
  max-height: 80vh;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;

  .loading-text {
    color: #3b82f6;
    font-size: 24px;
    font-weight: 600;
  }
}

.knowledge-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;

    &:hover {
      background: #9ca3af;
    }
  }
}

.knowledge-item {
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;

  &:hover {
    background: #f9fafb;
    border-color: #9ca3af;
  }

  .knowledge-term {
    color: #1f2937;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 12px;
  }

  .knowledge-definition {
    color: #374151;
    font-size: 22px;
    line-height: 1.5;
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;

  .empty-text {
    color: #6b7280;
    font-size: 24px;
    font-style: italic;
  }
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #d1d5db;

  .pagination-btn {
    background: #f9fafb;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    padding: 12px 20px;
    color: #374151;
    font-size: 22px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      background: #f3f4f6;
      border-color: #9ca3af;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .pagination-info {
    color: #1f2937;
    font-size: 22px;
    font-weight: 600;
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 640px) {
  .cyber-enhanced-memo-modal {
    padding: 4px;

    &__container {
      max-width: 100%;
      width: 99%;
      max-height: 95vh;
      padding: 20px;
    }

    &__title {
      font-size: 28px;
    }
  }

  .knowledge-section {
    padding: 16px;
    min-height: 600px;
    max-height: 75vh;
  }

  .knowledge-item {
    padding: 16px;

    .knowledge-term {
      font-size: 22px;
    }

    .knowledge-definition {
      font-size: 20px;
    }
  }

  .pagination-controls {
    flex-direction: column;
    gap: 12px;

    .pagination-btn {
      padding: 12px 20px;
      font-size: 20px;
    }

    .pagination-info {
      font-size: 20px;
    }
  }
}
</style>
