<template>
  <div class="ai-panel-wrapper">
    <!-- 模型和问题显示区域 - 在面板顶部 -->
    <div class="model-question-info" :class="panelColorClass">
      <div class="info-line">
        <span class="info-label">当前模型：</span>
        <span class="info-value">{{ modelName || title }}</span>
      </div>
      <div class="info-line">
        <span class="info-label">当前问题：</span>
        <span class="info-value">{{ currentQuestion || '暂无问题' }}</span>
      </div>
    </div>

    <div class="ai-panel">
      <!-- AI面板标题和控制区域 -->
      <div class="panel-header">
        <!-- 董会答介绍区域 -->
        <div class="assistant-intro">
          <div class="assistant-avatar">
            <img src="@/assets/assistant/董会答.png" alt="董会答" />
          </div>
          <div class="assistant-info">
            <div class="assistant-name">董会答</div>
            <div class="assistant-desc">懂你懂美团的问答助手</div>
          </div>
          <div class="assistant-actions">
            <div v-if="currentUserName" class="user-greeting">
              你好，{{ currentUserName }}
            </div>
            <button class="action-btn more-assistants" @click="$emit('go-back')">
              更多助手
            </button>
            <button class="action-btn toggle-memo" @click="toggleMemoSection">
              {{ showMemoSection ? '收起资料' : '展开资料' }}
            </button>
          </div>
        </div>

        <!-- 备忘录组件区域 -->
        <div v-if="showMemoSection" class="memo-section">
          <PersonalMemo
            ref="personalMemoRef"
            :user-id="currentUserId"
            :person-id="currentPersonId"
            @add-person-memo="$emit('add-person-memo')"
            @edit-person-memo="$emit('edit-person-memo', $event)"
            @delete-memo="$emit('delete-memo', $event)"
          />
          <IndustryMemo
            ref="industryMemoRef"
            @add-industry-memo="$emit('add-industry-memo')"
            @edit-industry-memo="$emit('edit-industry-memo', $event)"
            @delete-industry-memo="$emit('delete-industry-memo', $event)"
          />
          <CompanyKnowledgeBase @open-knowledge-dialog="$emit('open-knowledge-dialog')" />
          <PreInfo
            @add-pre-info="$emit('add-pre-info')"
          />
        </div>
      </div>

    <!-- 消息显示区域 -->
    <div class="messages-container" ref="messagesContainer">
      <!-- 历史消息 -->
      <template v-for="message in messages" :key="message.id">
        <!-- 用户消息 -->
        <div v-if="message.type === 'user'" class="message-item user-message">
          <div class="message-content">
            <div class="message-text">{{ message.content }}</div>
          </div>
        </div>

        <!-- AI消息 -->
        <div v-else-if="message.type === 'assistant'" class="message-item assistant-message">
          <!-- AI思考过程（历史） -->
          <div v-if="message.thinkingData && message.thinkingData.items.length > 0" class="thinking-section">
            <AgentThinkingProcess :thinking-data="message.thinkingData" />
          </div>
          <!-- AI回答 -->
          <div class="message-content">
            <AgentMessageRender :text="message.content" />
          </div>
        </div>
      </template>

      <!-- 当前思考过程（正在进行的） -->
      <div v-if="currentThinkingData.items.length > 0" class="message-item thinking-message">
        <AgentThinkingProcess :thinking-data="currentThinkingData" />
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="message-item loading-message">
        <div class="loading-dots">
          <span class="dot dot1">.</span>
          <span class="dot dot2">.</span>
          <span class="dot dot3">.</span>
        </div>
      </div>
    </div>

      <!-- 子输入区域 -->
      <SubInputSection
        ref="subInputRef"
        @send-message="handleSubMessage"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, computed } from 'vue';
import AgentThinkingProcess from './AgentThinkingProcess.vue';
import AgentMessageRender from './AgentMessageRender.vue';
import SubInputSection from './SubInputSection.vue';
import PersonalMemo from './PersonalMemo.vue';
import IndustryMemo from './IndustryMemo.vue';
import CompanyKnowledgeBase from './CompanyKnowledgeBase.vue';
import PreInfo from './PreInfo.vue';

interface IThinkingItem {
  type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result' | 'core_answer_start' | 'final_answer_complete';
  message?: string;
  step?: 'start' | 'processing' | 'complete';
  question?: string;
  questions?: string[];
  results?: Array<{ title: string; link: string }>;
}

interface IThinkingData {
  items: IThinkingItem[];
  isLoading: boolean;
}

interface IMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: number;
  thinkingData?: IThinkingData; // 助手消息包含完整的思考过程
}

interface IProps {
  title: string;
  modelName?: string; // 新增模型名称属性
  messages?: IMessage[];
  currentThinkingData?: IThinkingData;
  isLoading?: boolean;
  currentUserName?: string; // 当前用户名
  currentUserId?: string; // 当前用户ID
  currentPersonId?: string; // 当前人员ID
  currentQuestion?: string; // 当前问题
  panelIndex?: number; // 面板索引，用于区分颜色
}

const props = withDefaults(defineProps<IProps>(), {
  messages: () => [],
  currentThinkingData: () => ({ items: [], isLoading: false }),
  isLoading: false,
  modelName: '',
  currentUserName: '',
  currentUserId: '',
  currentPersonId: '',
  currentQuestion: '',
  panelIndex: 1,
});

const emit = defineEmits([
  'sub-message',
  'go-back',
  'add-person-memo',
  'edit-person-memo',
  'delete-memo',
  'add-industry-memo',
  'edit-industry-memo',
  'delete-industry-memo',
  'open-knowledge-dialog',
  'add-pre-info'
]);

// 备忘录区域显示状态（默认收起）
const showMemoSection = ref(false);

const messagesContainer = ref<HTMLElement | null>(null);
const subInputRef = ref<InstanceType<typeof SubInputSection> | null>(null);
const personalMemoRef = ref<InstanceType<typeof PersonalMemo> | null>(null);
const industryMemoRef = ref<InstanceType<typeof IndustryMemo> | null>(null);

// 切换备忘录区域显示
const toggleMemoSection = () => {
  showMemoSection.value = !showMemoSection.value;
};

// 根据面板索引计算颜色类
const panelColorClass = computed(() => {
  switch (props.panelIndex) {
    case 1:
      return 'panel-blue';
    case 2:
      return 'panel-yellow';
    case 3:
      return 'panel-purple';
    default:
      return 'panel-blue';
  }
});

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  });
};

// 处理子输入框的消息
const handleSubMessage = async (message: string) => {
  console.log('🚀 [AIPanel] 收到子输入框消息:', message);

  // 直接发送消息给父组件，不在这里处理主题生成
  emit('sub-message', message, props.panelIndex);
};

// 监听消息变化，自动滚动到底部
watch(
  () => [props.messages.length, props.currentThinkingData.items.length, props.isLoading],
  () => {
    scrollToBottom();
  },
  { deep: true }
);

// 暴露方法给父组件
defineExpose({
  scrollToBottom,
  subInputRef,
});
</script>

<style lang="scss" scoped>
.ai-panel-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
}

// 模型和问题显示区域 - 在面板顶部
.model-question-info {
  padding: 12px 16px;
  border-radius: 12px 12px 0 0;
  border: 2px solid rgba(139, 126, 216, 0.3);
  border-bottom: none;

  // 不同面板的背景色
  &.panel-blue {
    background: #e3f2fd; // 淡蓝色
  }

  &.panel-yellow {
    background: #fff8e1; // 淡黄色
  }

  &.panel-purple {
    background: #f3e5f5; // 淡紫色
  }

  .info-line {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    font-size: 16px; // 放大字体

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      font-weight: 600;
      color: #666;
      min-width: 80px;
    }

    .info-value {
      color: #333;
      flex: 1;
      word-break: break-all;
    }
  }
}

.ai-panel {
  display: flex;
  flex-direction: column;
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid rgba(139, 126, 216, 0.3);
  border-radius: 0 0 16px 16px;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.panel-header {
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, rgba(139, 126, 216, 0.1) 0%, rgba(183, 148, 246, 0.1) 100%);
  border-bottom: 1px solid rgba(139, 126, 216, 0.2);

  // 董会答介绍区域
  .assistant-intro {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    background: #f3f9ff; // 更浅的蓝色背景
    padding: 8px;

    .assistant-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .assistant-info {
      flex: 1;

      .assistant-name {
        font-size: 20px; // 放大字体
        font-weight: 600;
        color: hsl(238, 57%, 25%);
      }

      .assistant-desc {
        font-size: 16px; // 放大字体
        color: #333;
      }
    }

    .assistant-actions {
      display: flex;
      align-items: center;
      gap: 6px;

      .user-greeting {
        font-size: 16px; // 放大字体
        font-weight: 500;
        color: hsl(238, 57%, 25%);
        margin-right: 6px;
      }

      .action-btn {
        padding: 6px 8px;
        border: none;
        color: hsl(238, 57%, 25%);
        font-size: 16px; // 放大字体
        background-color: transparent;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 6px;

        &:hover {
          background: rgba(139, 126, 216, 0.2);
        }
      }
    }
  }

  // 模型和问题显示区域已移到外层

  // 备忘录组件区域
  .memo-section {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    border-bottom: 1px solid rgba(139, 126, 216, 0.1);
  }
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-item {
  &.user-message {
    align-self: flex-end;
    max-width: 80%;

    .message-content {
      background: linear-gradient(135deg, #8B7ED8 0%, #B794F6 100%);
      color: white;
      padding: 12px 16px;
      border-radius: 18px 18px 4px 18px;
      box-shadow: 0 2px 8px rgba(139, 126, 216, 0.3);

      .message-text {
        font-size: 16px;
        line-height: 1.5;
      }
    }
  }

  &.assistant-message {
    align-self: flex-start;
    max-width: 90%;

    .message-content {
      background: rgba(248, 249, 250, 0.9);
      border: 1px solid rgba(139, 126, 216, 0.2);
      padding: 16px;
      border-radius: 18px 18px 18px 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  &.thinking-message {
    align-self: flex-start;
    width: 100%;
  }

  .thinking-section {
    margin-bottom: 8px;
  }

  &.loading-message {
    align-self: flex-start;
    padding: 16px;
  }
}

.loading-dots {
  display: flex;
  gap: 4px;
  align-items: center;

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #8B7ED8;
    animation: loading-bounce 1.4s ease-in-out infinite both;

    &.dot1 {
      animation-delay: -0.32s;
    }

    &.dot2 {
      animation-delay: -0.16s;
    }

    &.dot3 {
      animation-delay: 0s;
    }
  }
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(139, 126, 216, 0.1);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(139, 126, 216, 0.3);
  border-radius: 3px;

  &:hover {
    background: rgba(139, 126, 216, 0.5);
  }
}
</style>
