<template>
  <div class="agent-response-wrapper">
    <!-- AI标识头部 -->
    <div class="ai-header">
      <div class="ai-avatar">🤖</div>
      <div class="ai-name">{{ aiName }}</div>
    </div>

    <!-- 思考过程组件 -->
    <AgentThinkingProcess
      v-if="thinkingData && (thinkingData.items.length > 0 || thinkingData.isLoading)"
      :thinking-data="thinkingData"
    />

    <!-- AI回答组件 -->
    <AgentChatItem
      v-if="assistantMessage && (assistantMessage.content || !assistantMessage.isFinish)"
      :message-data="assistantMessage"
    />
  </div>
</template>

<script setup lang="ts">
import AgentThinkingProcess from './AgentThinkingProcess.vue';
import AgentChatItem from './AgentChatItem.vue';

interface IThinkingData {
  items: Array<{
    type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result' | 'core_answer_start' | 'final_answer_complete';
    message?: string;
    step?: 'start' | 'processing' | 'complete';
    question?: string;
    questions?: string[];
    results?: Array<{ title: string; link: string }>;
  }>;
  isLoading: boolean;
}

interface IProps {
  aiName: string;
  thinkingData?: IThinkingData;
  assistantMessage?: IChatStreamContent;
}

defineProps<IProps>();
</script>

<style lang="scss" scoped>
.agent-response-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .ai-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: linear-gradient(135deg, rgba(139, 126, 216, 0.1) 0%, rgba(183, 148, 246, 0.15) 100%);
    border: 1px solid rgba(139, 126, 216, 0.3);
    border-radius: 12px;
    backdrop-filter: blur(10px);

    .ai-avatar {
      font-size: 24px;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(139, 126, 216, 0.2);
      border-radius: 50%;
    }

    .ai-name {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary, #333);
    }
  }
}
</style>
