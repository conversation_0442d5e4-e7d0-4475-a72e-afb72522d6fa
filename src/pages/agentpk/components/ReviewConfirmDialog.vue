<template>
  <div class="review-confirm-dialog-overlay">
    <div class="cyber-enhanced-confirm-modal" @click="$emit('close')">
      <div class="cyber-enhanced-confirm-modal__overlay" />

      <div
        class="cyber-enhanced-confirm-modal__container cyber-enhanced-confirm-modal__container--confirm"
        @click.stop
      >
        <button
          class="cyber-enhanced-confirm-modal__close-btn"
          aria-label="关闭"
          @click="$emit('close')"
        >
          ×
        </button>

        <div class="cyber-enhanced-confirm-modal__header">
          <h3 class="cyber-enhanced-confirm-modal__title">
            ✨ 精益求精
          </h3>
          <div class="cyber-enhanced-confirm-modal__divider"></div>
        </div>

        <div class="cyber-enhanced-confirm-modal__content">
          <!-- 感谢信息 -->
          <div class="thank-message">
            感谢您的复盘建议！是否根据您的反馈重新生成一个更好的回答？
          </div>

          <!-- 复盘建议展示 -->
          <div class="suggestion-display">
            <div class="suggestion-title">您的复盘建议：</div>
            <div class="suggestion-content">{{ reviewSuggestion }}</div>
          </div>

          <!-- 说明信息 -->
          <div class="info-section">
            <div class="info-icon">💡</div>
            <div class="info-content">
              <div class="info-title">重新生成将结合：</div>
              <ul class="info-list">
                <li>原始问题和答案</li>
                <li>您的复盘建议</li>
                <li>相关背景资料</li>
                <li>最新搜索信息</li>
              </ul>
            </div>
          </div>

          <div class="cyber-enhanced-confirm-modal__actions">
            <button
              class="confirm-btn-cancel"
              @click="$emit('close')"
            >
              暂不重新生成
            </button>
            <button
              class="confirm-btn-submit"
              @click="handleConfirm"
            >
              重新生成答案
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Props定义
interface IProps {
  reviewSuggestion: string;
}

defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  confirm: [];
}>();

// 处理确认重新生成
const handleConfirm = () => {
  emit('confirm');
};
</script>

<style lang="scss" scoped>
.review-confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2100; // 比第一个弹窗层级更高
  animation: fadeIn 0.3s ease-out;
  pointer-events: auto;
  padding: 40px 20px;
  box-sizing: border-box;
}

.cyber-enhanced-confirm-modal {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;

  &__overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
  }

  &__container {
    position: relative;
    max-width: 800px;
    width: 95%;
    padding: 40px;
    border-radius: 24px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);

    &--confirm {
      background: #ffffff; // 白色背景
      border: 1px solid #e5e7eb;
    }
  }

  &__close-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: #6b7280;
    font-size: 24px;
    line-height: 1;
    transition: all 0.2s ease;
    border: none;
    background: transparent;

    &:hover {
      color: #374151;
    }
  }

  &__header {
    margin-bottom: 16px;
  }

  &__title {
    margin: 0 0 16px 0;
    font-size: 32px;
    font-weight: 600;
    color: #1f2937;
    text-align: left;
  }

  &__divider {
    width: 100%;
    height: 1px;
    background: #d1d5db;
    margin: 0;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 28px;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
  }
}

.thank-message {
  color: #1f2937;
  font-size: 28px;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  padding: 20px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 12px;
}

.suggestion-display {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  padding: 20px;

  .suggestion-title {
    color: #1f2937;
    font-size: 26px;
    font-weight: 600;
    margin-bottom: 12px;
  }

  .suggestion-content {
    color: #374151;
    font-size: 24px;
    line-height: 1.5;
    background: #ffffff;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
    border: 1px solid #e5e7eb;
  }
}

.info-section {
  display: flex;
  gap: 16px;
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 12px;
  padding: 20px;

  .info-icon {
    font-size: 32px;
    flex-shrink: 0;
  }

  .info-content {
    flex: 1;

    .info-title {
      color: #1f2937;
      font-size: 26px;
      font-weight: 600;
      margin-bottom: 12px;
    }

    .info-list {
      margin: 0;
      padding-left: 20px;

      li {
        color: #374151;
        font-size: 24px;
        line-height: 1.6;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 按钮样式
.confirm-btn-cancel,
.confirm-btn-submit {
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

.confirm-btn-cancel {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  color: #374151;

  &:hover:not(:disabled) {
    background: #f3f4f6;
  }
}

.confirm-btn-submit {
  background: #3b82f6;
  border: 1px solid #3b82f6;
  color: white;

  &:hover:not(:disabled) {
    background: #2563eb;
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 640px) {
  .cyber-enhanced-confirm-modal {
    padding: 8px;

    &__container {
      max-width: 100%;
      padding: 16px;
    }

    &__actions {
      flex-direction: column;
      gap: 8px;
    }
  }
}
</style>
