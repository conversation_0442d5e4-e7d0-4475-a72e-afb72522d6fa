<template>
  <div class="review-dialog-overlay">
    <div class="cyber-enhanced-review-modal" @click="$emit('close')">
      <div class="cyber-enhanced-review-modal__overlay" />

      <div
        class="cyber-enhanced-review-modal__container cyber-enhanced-review-modal__container--review"
        @click.stop
      >
        <button
          class="cyber-enhanced-review-modal__close-btn"
          aria-label="关闭"
          @click="$emit('close')"
        >
          ×
        </button>

        <div class="cyber-enhanced-review-modal__header">
          <h3 class="cyber-enhanced-review-modal__title">
            📝 回答复盘
          </h3>
          <div class="cyber-enhanced-review-modal__divider"></div>
        </div>

        <div class="cyber-enhanced-review-modal__content">
          <!-- 便捷输入部分 -->
          <div class="quick-inputs">
            <p class="quick-inputs-label">便捷输入：</p>
            <div class="quick-inputs-grid">
              <button
                v-for="option in quickOptions"
                :key="option.text"
                class="quick-input-btn"
                @click="handleQuickInput(option.text)"
              >
                <span class="quick-input-text">{{ option.text }}</span>
              </button>
            </div>
          </div>

          <!-- 复盘建议输入框 -->
          <div class="review-input-section">
            <label for="reviewText">复盘建议：</label>
            <div class="textarea-wrapper">
              <textarea
                id="reviewText"
                ref="reviewInputRef"
                v-model="reviewSuggestion"
                class="cyber-enhanced-review-modal__textarea"
                placeholder="请输入您的复盘建议..."
                rows="6"
              />
              <!-- 语音按钮在多行输入框内部右上角 -->
              <div
                class="voice-toggle-inner textarea-voice"
                :class="{
                  breathing: isRecording,
                }"
                @click="handleVoiceButtonClick"
              >
                <MicrophoneIcon :size="16" color="#000000" />
              </div>
            </div>
          </div>

          <div class="cyber-enhanced-review-modal__actions">
            <button
              class="review-btn-cancel"
              @click="$emit('close')"
            >
              取消
            </button>
            <button
              class="review-btn-submit"
              :disabled="!reviewSuggestion.trim()"
              @click="handleSubmit"
            >
              提交复盘
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount, nextTick } from 'vue';
import { showToast } from 'vant';
import MicrophoneIcon from '@/assets/icons/MicrophoneIcon.vue';
import { getStreamAsr } from '../apis/tts';
import Recorder from 'recorder-realtime';
import { generateRandomString } from '@/utils';

// Emits定义
const emit = defineEmits<{
  close: [];
  submit: [suggestion: string];
}>();

// 响应式数据
const reviewSuggestion = ref('');

// 语音相关数据
const micPermission = ref(false);
const sessionId = ref('');
const audioBufferIndex = ref(0);
const lastBuffer = ref();
const voiceMessage = ref('');
const isRecording = ref(false);
const lastVoiceText = ref('');

// 录音相关变量
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let timerId: ReturnType<typeof setTimeout> | null = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let noTextTimerId: ReturnType<typeof setTimeout> | null = null;
let mediaStream: MediaStream | null = null;

// 输入框引用
const reviewInputRef = ref();

// 便捷输入选项
const quickOptions = [
  { text: '回答更简短' },
  { text: '回答更详细' },
  { text: '搜索更多参考资料' },
  { text: '需要更多数据支撑' },
  { text: '时效性信息需要更新' },
  { text: '需要更多实例说明' },
];

// 处理便捷输入
const handleQuickInput = (text: string) => {
  if (reviewSuggestion.value.trim()) {
    reviewSuggestion.value = `${reviewSuggestion.value}；${text}`;
  } else {
    reviewSuggestion.value = text;
  }
};

// 处理提交
const handleSubmit = () => {
  if (reviewSuggestion.value.trim()) {
    emit('submit', reviewSuggestion.value.trim());
  }
};

// 语音相关方法
// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => track.stop());
    mediaStream = null;
  }
};

// 设置麦克风权限
async function setMicPermission() {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
}

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
    return;
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    cancelRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      try {
        const streamData = await getStreamAsr({
          sessionId: sessionId.value,
          format: 'pcm',
          sampleRate: 16000,
          index: audioBufferIndex.value,
          data: data.buffer,
        });

        if (
          streamData.data.full_text &&
          streamData.data.full_text.trim() !== '' &&
          streamData.data.full_text !== lastVoiceText.value
        ) {
          const newText = streamData.data.full_text;
          const previousText = lastVoiceText.value;

          let textToInsert = newText;
          if (previousText && newText.startsWith(previousText)) {
            textToInsert = newText.slice(previousText.length);
          }

          if (textToInsert) {
            insertTextAtCursor(textToInsert);
          }

          lastVoiceText.value = newText;
          voiceMessage.value = newText;

          // 重置无文字定时器
          if (noTextTimerId !== null) {
            clearTimeout(noTextTimerId);
            noTextTimerId = null;
          }
        }
      } catch (error) {
        console.error('❌ [ReviewDialog] 语音识别失败:', error);
      }
    }
  };
};

// 在光标位置插入文字
const insertTextAtCursor = (newText: string) => {
  if (!reviewInputRef.value) return;

  const inputElement = reviewInputRef.value;
  const start = (inputElement.selectionStart as number) || 0;
  const end = (inputElement.selectionEnd as number) || 0;
  const currentValue = reviewSuggestion.value;

  const newValue = currentValue.slice(0, start) + newText + currentValue.slice(end);
  reviewSuggestion.value = newValue;

  const newCursorPosition = start + newText.length;

  void nextTick(() => {
    inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
    inputElement.focus();
  });
};

// 开始录音
async function startRecording() {
  if (isRecording.value) {
    await stopRecording();
    return;
  }

  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    lastVoiceText.value = '';
    voiceMessage.value = '';

    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    // 最大录音时长定时器
    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopRecording();
    }, 1000 * 60);

    // 2秒内无文字识别自动停止录音
    noTextTimerId = setTimeout(async () => {
      if (isRecording.value && !voiceMessage.value.trim()) {
        showToast('未识别到语音内容，录音已停止');
        await stopRecording();
      }
    }, 2000);
  }
}

// 结束录音
async function stopRecording() {
  if (!isRecording.value) return;

  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (noTextTimerId !== null) {
    clearTimeout(noTextTimerId);
    noTextTimerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });

  if (voiceMessage.value) {
    console.log('📤 [ReviewDialog] 语音识别完成:', voiceMessage.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }

  voiceMessage.value = '';
  lastVoiceText.value = '';
}

// 取消录音
const cancelRecording = () => {
  if (isRecording.value) {
    isRecording.value = false;
    if (timerId !== null) {
      clearTimeout(timerId);
      timerId = null;
    }
    if (noTextTimerId !== null) {
      clearTimeout(noTextTimerId);
      noTextTimerId = null;
    }
    if (recorder) {
      recorder.stop();
    }
    releaseMicrophoneResources();
    voiceMessage.value = '';
    lastVoiceText.value = '';
  }
};

// 处理语音按钮点击
const handleVoiceButtonClick = async () => {
  await startRecording();
};

// 组件卸载时释放麦克风资源
onBeforeUnmount(() => {
  console.log('🧹 [ReviewDialog] 组件卸载，释放麦克风资源');
  if (isRecording.value) {
    if (recorder) {
      recorder.stop();
    }
    isRecording.value = false;
  }
  releaseMicrophoneResources();
});
</script>

<style lang="scss" scoped>
.review-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
  pointer-events: auto;
  padding: 40px 20px;
  box-sizing: border-box;
}

.cyber-enhanced-review-modal {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;

  &__overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
  }

  &__container {
    position: relative;
    max-width: 800px;
    width: 95%;
    padding: 40px;
    border-radius: 24px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);

    &--review {
      background: #ffffff; // 白色背景
      border: 1px solid #e5e7eb;
    }
  }

  &__close-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: #6b7280;
    font-size: 24px;
    line-height: 1;
    transition: all 0.2s ease;
    border: none;
    background: transparent;

    &:hover {
      color: #374151;
    }
  }

  &__header {
    margin-bottom: 16px;
  }

  &__title {
    margin: 0 0 16px 0;
    font-size: 32px;
    font-weight: 600;
    color: #1f2937;
    text-align: left;
  }

  &__divider {
    width: 100%;
    height: 1px;
    background: #d1d5db;
    margin: 0;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 28px;
  }

  &__textarea {
    width: 100%;
    padding: 20px;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    font-size: 24px;
    line-height: 1.5;
    resize: vertical;
    min-height: 200px;

    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    &::placeholder {
      color: #9ca3af;
    }
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
  }
}

// 快捷输入样式
.quick-inputs {
  margin-bottom: 24px;

  &-label {
    margin: 0 0 16px 0;
    font-size: 24px;
    font-weight: 500;
    color: #1f2937;
  }

  &-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

.quick-input-btn {
  padding: 16px 20px;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  background: #f9fafb;
  color: #1f2937;
  font-size: 22px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 12px;

  &:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
  }

  .quick-input-text {
    flex: 1;
  }
}

// 复盘输入区域
.review-input-section {
  display: flex;
  flex-direction: column;
  gap: 16px;

  label {
    font-size: 24px;
    font-weight: 500;
    color: #1f2937;
  }

  .textarea-wrapper {
    position: relative;
  }
}

// 按钮样式
.review-btn-cancel,
.review-btn-submit {
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

.review-btn-cancel {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  color: #374151;

  &:hover:not(:disabled) {
    background: #f3f4f6;
  }
}

.review-btn-submit {
  background: #3b82f6;
  border: 1px solid #3b82f6;
  color: white;

  &:hover:not(:disabled) {
    background: #2563eb;
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 语音按钮样式
.voice-toggle-inner {
  position: absolute;
  right: 12px;
  top: 12px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  background: transparent;
  border: none;
  transition: all 0.2s ease;
  z-index: 10;

  &.breathing {
    animation: breathing 2s ease-in-out infinite;
  }
}

// 动画定义
@keyframes breathing {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 8px transparent;
  }
}

// 响应式设计
@media (max-width: 640px) {
  .cyber-enhanced-review-modal {
    padding: 8px;

    &__container {
      max-width: 100%;
      padding: 16px;
    }

    &__textarea {
      min-height: 100px;
    }

    &__actions {
      flex-direction: column;
      gap: 8px;
    }

    .quick-inputs-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
