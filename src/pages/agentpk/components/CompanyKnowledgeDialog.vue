<template>
  <Teleport to="body">
    <div
      v-if="isOpen"
      class="cyber-company-knowledge-dialog"
      @click="handleClose"
    >
      <!-- 遮罩层 -->
      <div class="cyber-company-knowledge-dialog__overlay" />

      <!-- 弹窗主体 -->
      <div
        class="cyber-company-knowledge-dialog__container"
        @click.stop
      >
        <!-- 弹窗头部 -->
        <div class="cyber-company-knowledge-dialog__header">
          <div class="cyber-company-knowledge-dialog__title">
            <svg class="cyber-company-knowledge-dialog__icon" viewBox="0 0 24 24" fill="#4169e1">
              <path d="M12 21V5a7 7 0 0 0-7 7v9h7m0-16a7 7 0 0 1 7 7v9h-7" />
            </svg>
            <span class="cyber-company-knowledge-dialog__title-text">
              公司知识库 ({{ knowledgeData.length }} 个知识条目)
            </span>
          </div>
          <button
            class="cyber-company-knowledge-dialog__close-btn"
            @click="handleClose"
            aria-label="关闭弹窗"
          >
            <X class="cyber-company-knowledge-dialog__close-icon" />
          </button>
        </div>

        <!-- 内容区域 -->
        <div class="cyber-company-knowledge-dialog__content">
          <textarea
            v-model="currentText"
            readonly
            class="cyber-company-knowledge-dialog__textarea"
          />

          <!-- 翻页控制 -->
          <div
            v-if="totalPages > 1"
            class="cyber-company-knowledge-dialog__pagination"
          >
            <button
              class="cyber-company-knowledge-dialog__page-btn"
              :class="{ 'cyber-company-knowledge-dialog__page-btn--disabled': currentPage === 0 }"
              :disabled="currentPage === 0"
              @click="handlePrevPage"
            >
              <ChevronLeft class="cyber-company-knowledge-dialog__page-icon" />
              上一页
            </button>

            <span class="cyber-company-knowledge-dialog__page-info">
              第 {{ currentPage + 1 }} 页，共 {{ totalPages }} 页
            </span>

            <button
              class="cyber-company-knowledge-dialog__page-btn"
              :class="{ 'cyber-company-knowledge-dialog__page-btn--disabled': currentPage === totalPages - 1 }"
              :disabled="currentPage === totalPages - 1"
              @click="handleNextPage"
            >
              下一页
              <ChevronRight class="cyber-company-knowledge-dialog__page-icon" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { X, ChevronLeft, ChevronRight } from 'lucide-vue-next';

interface KnowledgeItem {
  title?: string;
  term?: string;
  name?: string;
  content?: string;
  explain?: string;
  definition?: string;
  source?: string;
}

interface Props {
  isOpen: boolean;
  knowledgeData?: KnowledgeItem[];
}

interface Emits {
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {
  knowledgeData: () => []
});

const emit = defineEmits<Emits>();

// 响应式数据
const currentPage = ref(0);
const itemsPerPage = 18;

// 格式化知识库数据为文本
const formattedText = computed(() => {
  if (!props.knowledgeData || props.knowledgeData.length === 0) {
    return '暂无公司知识库数据';
  }

  // 按来源分组并优先展示 business.json 的条目
  const businessItems: string[] = [];
  const otherItems: string[] = [];

  props.knowledgeData.forEach(item => {
    const title = item.title || item.term || item.name || '未知条目';
    const content = item.content || item.explain || item.definition || '';

    const line = content ? `${title} - ${content}` : title;

    if (item.source === 'business.json') {
      businessItems.push(line);
    } else {
      otherItems.push(line);
    }
  });

  // 合并并编号
  const allItems = [...businessItems, ...otherItems];
  return allItems
    .map((line, index) => `${index + 1}. ${line}`)
    .join('\n');
});

// 分页处理
const totalPages = computed(() =>
  Math.ceil(formattedText.value.split('\n').length / itemsPerPage)
);

const currentText = computed(() => {
  const lines = formattedText.value.split('\n');
  const startIndex = currentPage.value * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  return lines.slice(startIndex, endIndex).join('\n');
});

// 翻页处理
const handlePrevPage = () => {
  if (currentPage.value > 0) {
    currentPage.value--;
  }
};

const handleNextPage = () => {
  if (currentPage.value < totalPages.value - 1) {
    currentPage.value++;
  }
};

// 关闭弹窗
const handleClose = () => {
  emit('close');
};

// 重置页码
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    currentPage.value = 0;
  }
});
</script>

<style lang="scss" scoped>
.cyber-company-knowledge-dialog {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;

  &__overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
  }

  &__container {
    position: relative;
    max-width: 672px; // max-w-2xl
    width: 100%;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05), 0 4px 12px -2px rgba(0, 0, 0, 0.1);
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 12px;
    border-bottom: 1px solid #e2e8f0;
    background: #fff;
  }

  &__title {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  &__icon {
    width: 16px;
    height: 16px;
  }

  &__title-text {
    font-size: 14px;
    font-weight: 500;
    color: #4169e1;
    white-space: nowrap;
  }

  &__close-btn {
    padding: 4px;
    border-radius: 50%;
    color: #94a3b8;
    line-height: 0;
    transition: background-color 0.2s ease;

    &:hover {
      background: #f1f5f9;
    }
  }

  &__close-icon {
    width: 14px;
    height: 14px;
  }

  &__content {
    padding: 16px;
    background: #f8fafc;
  }

  &__textarea {
    width: 100%;
    height: 400px;
    padding: 12px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    color: #854d0e;
    background: #ffffff;
    border: 1px solid rgb(235, 224, 171);
    border-radius: 6px;
    resize: none;
    overflow: auto;

    &:focus {
      outline: none;
    }
  }

  &__pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px;
  }

  &__page-btn {
    display: flex;
    align-items: center;
    padding: 4px 12px;
    font-size: 14px;
    color: #854d0e;
    background: #fef08a;
    border-radius: 4px;
    transition: all 0.2s ease;
    cursor: pointer;

    &--disabled {
      color: #fde047;
      background: transparent;
      cursor: not-allowed;
    }

    &:not(&--disabled):hover {
      background: #fde047;
    }
  }

  &__page-icon {
    width: 16px;
    height: 16px;

    &:first-child {
      margin-right: 4px;
    }

    &:last-child {
      margin-left: 4px;
    }
  }

  &__page-info {
    font-size: 14px;
    color: #854d0e;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .cyber-company-knowledge-dialog {
    padding: 8px;

    &__container {
      max-width: 100%;
    }

    &__textarea {
      height: 300px;
      font-size: 12px;
    }

    &__pagination {
      // 保持左右排列，不改为上下排布
      gap: 8px;
    }

    &__page-btn {
      // 在小屏幕上缩小按钮文字和间距
      padding: 4px 8px;
      font-size: 12px;
    }

    &__page-info {
      font-size: 12px;
      // 在极小屏幕上可以隐藏页码信息，只保留按钮
      @media (max-width: 480px) {
        display: none;
      }
    }
  }
}
</style>
