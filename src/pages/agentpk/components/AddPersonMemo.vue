<template>
  <div class="add-person-memo-overlay">
    <div
      v-if="true"
      class="cyber-enhanced-memo-modal"
      @click="handleClose"
    >
      <div class="cyber-enhanced-memo-modal__overlay" />

      <div
        class="cyber-enhanced-memo-modal__container cyber-enhanced-memo-modal__container--personal"
        @click.stop
      >
        <button
          class="cyber-enhanced-memo-modal__close-btn"
          aria-label="关闭"
          @click="handleClose"
        >
          ×
        </button>

        <div class="cyber-enhanced-memo-modal__header">
          <h3 class="cyber-enhanced-memo-modal__title">
            📝 添加个人备忘录
          </h3>
          <div class="cyber-enhanced-memo-modal__divider"></div>
        </div>

        <div class="cyber-enhanced-memo-modal__content">
          <!-- 便捷输入部分 -->
          <div class="quick-inputs">
            <p class="quick-inputs-label">快捷输入：</p>
            <div class="quick-inputs-grid">
              <button
                v-for="(item, index) in quickInputItems"
                :key="index"
                class="quick-input-btn"
                @click="handleQuickInput(item.text)"
              >
                <span class="quick-input-icon">{{ item.icon }}</span>
                <span class="quick-input-text">{{ item.label }}</span>
              </button>
            </div>
          </div>

          <div class="memo-input-section">
            <label for="memoText">备忘录内容：</label>
            <div class="textarea-wrapper">
              <textarea
                id="memoText"
                ref="memoInputRef"
                v-model="memoContent"
                class="cyber-enhanced-memo-modal__textarea"
                placeholder="记录一些重要信息..."
                rows="5"
                maxlength="1000"
              />
              <!-- 语音按钮在多行输入框内部右上角 -->
              <div
                class="voice-toggle-inner textarea-voice"
                :class="{
                  breathing: isRecording,
                }"
                @click="handleVoiceButtonClick"
              >
                <MicrophoneIcon :size="16" color="#000000" />
              </div>
            </div>
            <div class="memo-input-hint">
              支持多行输入，记录对您有价值的信息 ({{ memoContent.length }}/1000)
            </div>
          </div>

          <div class="cyber-enhanced-memo-modal__actions">
            <button
              class="memo-btn-cancel"
              :disabled="isSaving"
              @click="handleClose"
            >
              取消
            </button>
            <button
              class="memo-btn-submit"
              :disabled="!memoContent.trim() || isSaving"
              @click="handleSave"
            >
              {{ isSaving ? '保存中...' : '保存备忘录' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount, nextTick } from 'vue';
import { showToast } from 'vant';
import MicrophoneIcon from '@/assets/icons/MicrophoneIcon.vue';
import { getStreamAsr } from '../apis/tts';
import { addPersonEventNatural } from '@/apis/memory';
import Recorder from 'recorder-realtime';
import { generateRandomString } from '@/utils';

// Props定义
interface IProps {
  userId: string;
  personId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  save: [content: string];
}>();

// 响应式数据
const memoContent = ref('');
const isSaving = ref(false);

// 便捷输入选项
const quickInputItems = [
  { label: '个人偏好记录', text: '个人偏好记录', icon: '❤️' },
  { label: '重要联系方式', text: '重要联系方式', icon: '📞' },
  { label: '生活习惯记录', text: '生活习惯记录', icon: '🏠' },
  { label: '学习计划安排', text: '学习计划安排', icon: '📚' },
  { label: '健康状况记录', text: '健康状况记录', icon: '🏥' },
  { label: '兴趣爱好记录', text: '兴趣爱好记录', icon: '🎨' },
];

// 语音相关数据
const micPermission = ref(false);
const sessionId = ref('');
const audioBufferIndex = ref(0);
const lastBuffer = ref();
const voiceMessage = ref('');
const isRecording = ref(false);
const lastVoiceText = ref('');

// 录音相关变量
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let timerId: ReturnType<typeof setTimeout> | null = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let noTextTimerId: ReturnType<typeof setTimeout> | null = null;
let mediaStream: MediaStream | null = null;

// 输入框引用
const memoInputRef = ref();

// 便捷输入处理
const handleQuickInput = (text: string) => {
  const currentContent = memoContent.value;
  const newContent = currentContent ? `${currentContent}\n${text}` : text;
  memoContent.value = newContent;

  // 聚焦到输入框末尾
  void nextTick(() => {
    if (memoInputRef.value) {
      memoInputRef.value.focus();
      const { length } = memoContent.value;
      memoInputRef.value.setSelectionRange(length, length);
    }
  });
};

// 处理关闭
const handleClose = () => {
  // 重置表单
  memoContent.value = '';
  emit('close');
};



// 处理保存
const handleSave = async () => {
  if (!memoContent.value.trim()) {
    showToast('请输入备忘录内容');
    return;
  }

  try {
    isSaving.value = true;

    console.log('🔄 [AddPersonMemo] 保存备忘录:', {
      userId: props.userId,
      personId: props.personId,
      content: memoContent.value.trim(),
    });

    // 调用 add_person_event API
    const response = await addPersonEventNatural({
      user_id: props.userId,
      person_id: props.personId,
      event_text: memoContent.value.trim(),
    });

    console.log('📡 [AddPersonMemo] API响应:', response);

    if (response.result === 'success') {
      // 继续维持保存中一秒钟
      await new Promise<void>(resolve => {
        setTimeout(resolve, 1000);
      });

      // 通知父组件保存成功
      emit('save', memoContent.value.trim());

      showToast('保存成功');

      // 关闭对话框
      handleClose();
    } else {
      console.warn('⚠️ [AddPersonMemo] 保存失败:', response);
      showToast('保存失败，请重试');
    }
  } catch (error) {
    console.error('❌ [AddPersonMemo] 保存备忘录失败:', error);
    showToast('保存失败，请重试');
  } finally {
    isSaving.value = false;
  }
};

// 语音相关方法
// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => track.stop());
    mediaStream = null;
  }
};

// 设置麦克风权限
async function setMicPermission() {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
}

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
    return;
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    cancelRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      try {
        const streamData = await getStreamAsr({
          sessionId: sessionId.value,
          format: 'pcm',
          sampleRate: 16000,
          index: audioBufferIndex.value,
          data: data.buffer,
        });

        if (
          streamData.data.full_text &&
          streamData.data.full_text.trim() !== '' &&
          streamData.data.full_text !== lastVoiceText.value
        ) {
          const newText = streamData.data.full_text;
          const previousText = lastVoiceText.value;

          let textToInsert = newText;
          if (previousText && newText.startsWith(previousText)) {
            textToInsert = newText.slice(previousText.length);
          }

          if (textToInsert) {
            insertTextAtCursor(textToInsert);
          }

          lastVoiceText.value = newText;
          voiceMessage.value = newText;

          // 重置无文字定时器
          if (noTextTimerId !== null) {
            clearTimeout(noTextTimerId);
            noTextTimerId = null;
          }
        }
      } catch (error) {
        console.error('❌ [AddPersonMemo] 语音识别失败:', error);
      }
    }
  };
};

// 在光标位置插入文字
const insertTextAtCursor = (newText: string) => {
  if (!memoInputRef.value) return;

  const inputElement = memoInputRef.value;
  const start = (inputElement.selectionStart as number) || 0;
  const end = (inputElement.selectionEnd as number) || 0;
  const currentValue = memoContent.value;

  const newValue = currentValue.slice(0, start) + newText + currentValue.slice(end);
  memoContent.value = newValue;

  const newCursorPosition = start + newText.length;

  void nextTick(() => {
    inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
    inputElement.focus();
  });
};

// 开始录音
async function startRecording() {
  if (isRecording.value) {
    await stopRecording();
    return;
  }

  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    lastVoiceText.value = '';
    voiceMessage.value = '';

    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    // 最大录音时长定时器
    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopRecording();
    }, 1000 * 60);

    // 2秒内无文字识别自动停止录音
    noTextTimerId = setTimeout(async () => {
      if (isRecording.value && !voiceMessage.value.trim()) {
        showToast('未识别到语音内容，录音已停止');
        await stopRecording();
      }
    }, 2000);
  }
}

// 结束录音
async function stopRecording() {
  if (!isRecording.value) return;

  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (noTextTimerId !== null) {
    clearTimeout(noTextTimerId);
    noTextTimerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });

  if (voiceMessage.value) {
    console.log('📤 [AddPersonMemo] 语音识别完成:', voiceMessage.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }

  voiceMessage.value = '';
  lastVoiceText.value = '';
}

// 取消录音
const cancelRecording = () => {
  if (isRecording.value) {
    isRecording.value = false;
    if (timerId !== null) {
      clearTimeout(timerId);
      timerId = null;
    }
    if (noTextTimerId !== null) {
      clearTimeout(noTextTimerId);
      noTextTimerId = null;
    }
    if (recorder) {
      recorder.stop();
    }
    releaseMicrophoneResources();
    voiceMessage.value = '';
    lastVoiceText.value = '';
  }
};

// 处理语音按钮点击
const handleVoiceButtonClick = async () => {
  await startRecording();
};

// 组件卸载时释放麦克风资源
onBeforeUnmount(() => {
  console.log('🧹 [AddPersonMemo] 组件卸载，释放麦克风资源');
  if (isRecording.value) {
    if (recorder) {
      recorder.stop();
    }
    isRecording.value = false;
  }
  releaseMicrophoneResources();
});
</script>

<style lang="scss" scoped>
.add-person-memo-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
  pointer-events: auto;
  padding: 40px 20px;
  box-sizing: border-box;
}

.cyber-enhanced-memo-modal {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;

  &__overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
  }

  &__container {
    position: relative;
    max-width: 800px;
    width: 95%;
    padding: 40px;
    border-radius: 24px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);

    &--personal {
      background: #ffffff; // 白色背景
      border: 1px solid #e5e7eb;
    }
  }

  &__close-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: #6b7280;
    font-size: 24px;
    line-height: 1;
    transition: all 0.2s ease;
    border: none;
    background: transparent;

    &:hover {
      color: #374151;
    }
  }

  &__header {
    margin-bottom: 16px;
  }

  &__title {
    margin: 0 0 16px 0;
    font-size: 32px;
    font-weight: 600;
    color: #1f2937;
    text-align: left;
  }

  &__divider {
    width: 100%;
    height: 1px;
    background: #d1d5db;
    margin: 0;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 28px;
  }

  &__textarea {
    width: 100%;
    padding: 20px;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    font-size: 24px;
    line-height: 1.5;
    resize: vertical;
    min-height: 200px;

    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    &::placeholder {
      color: #9ca3af;
    }
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
  }
}

// 快捷输入样式
.quick-inputs {
  margin-bottom: 24px;

  &-label {
    margin: 0 0 16px 0;
    font-size: 24px;
    font-weight: 500;
    color: #1f2937;
  }

  &-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

.quick-input-btn {
  padding: 16px 20px;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  background: #f9fafb;
  color: #1f2937;
  font-size: 22px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 12px;

  &:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
  }

  .quick-input-icon {
    font-size: 24px;
  }

  .quick-input-text {
    flex: 1;
  }
}

// 备忘录输入区域
.memo-input-section {
  display: flex;
  flex-direction: column;
  gap: 16px;

  label {
    font-size: 24px;
    font-weight: 500;
    color: #1f2937;
  }

  .textarea-wrapper {
    position: relative;
  }

  .memo-input-hint {
    font-size: 20px;
    color: #6b7280;
    text-align: right;
  }
}

// 按钮样式
.memo-btn-cancel,
.memo-btn-submit {
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

.memo-btn-cancel {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  color: #374151;

  &:hover:not(:disabled) {
    background: #f3f4f6;
  }
}

.memo-btn-submit {
  background: #3b82f6;
  border: 1px solid #3b82f6;
  color: white;

  &:hover:not(:disabled) {
    background: #2563eb;
  }
}

// 语音按钮样式
.voice-toggle-inner {
  position: absolute;
  right: 12px;
  top: 12px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  background: transparent;
  border: none;
  transition: all 0.2s ease;
  z-index: 10;

  &.breathing {
    animation: breathing 2s ease-in-out infinite;
  }
}

// 动画定义
@keyframes breathing {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 8px transparent;
  }
}

// 响应式设计
@media (max-width: 640px) {
  .cyber-enhanced-memo-modal {
    padding: 8px;

    &__container {
      max-width: 100%;
      padding: 16px;
    }

    &__textarea {
      min-height: 100px;
    }

    &__actions {
      flex-direction: column;
      gap: 8px;
    }

    .quick-inputs-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
