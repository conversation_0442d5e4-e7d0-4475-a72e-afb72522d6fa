import http from '@/lib/http';

// 历史聊天记录相关接口
export interface IHistoryMessage {
  type: 'human' | 'ai';
  content: string;
  additional_kwargs: Record<string, unknown>;
}

export interface IConversationHistoryResponse {
  conversation_id: string;
  history: IHistoryMessage[];
  status: string;
}

// 获取历史聊天记录
export function getConversationHistory(
  conversationId: string,
  userId: string,
): Promise<IConversationHistoryResponse> {
  return http({
    url: `/humanrelation/history?conversation_id=${conversationId}&user_id=${userId}`,
    method: 'get',
  });
}
