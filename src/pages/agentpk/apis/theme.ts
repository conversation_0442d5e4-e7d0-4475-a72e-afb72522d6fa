import fetchInstance from '@/lib/fetch';

// 主题生成请求接口
export interface IThemeGenerationRequest {
  user_id: string;
  user_message: string;
}

// 主题方案接口
export interface IThemeScheme {
  background: string;
  cardBackground: string;
  textColor: string;
  headingColor: string;
  accentColor: string;
  borderColor: string;
  icons: string[];
}

// 主题生成响应接口
export interface IThemeGenerationResponse {
  success: boolean;
  error: string | null;
  response: {
    id: string;
    object: string;
    created: number;
    model: string;
    usage: {
      completion_tokens: number;
      prompt_tokens: number;
      total_tokens: number;
      cache_write_tokens: number;
      cache_read_tokens: number;
      input_tokens: number;
      output_tokens: number;
    };
    choices: Array<{
      index: number;
      message: {
        role: string;
        content: string;
        tool_calls: any[];
        task_id: string | null;
        reasoning_content: string;
        reasoning_details: any;
      };
      finish_reason: string;
      logprobs: any;
    }>;
  };
}

// 生成主题方案
export async function generateTheme(params: IThemeGenerationRequest): Promise<IThemeScheme> {
  console.log('📤 [theme.ts] generateTheme API调用开始:', {
    url: '/humanrelation/ai_call',
    method: 'POST',
    params,
  });

  try {
    const requestBody = {
      messages: [
        {
          role: "user",
          content: `基于用户的描述："${params.user_message}"，为网页演示文稿生成一个完整的主题方案。请返回JSON格式，包含以下字段：
{
  "background": "主背景渐变色CSS代码，使用linear-gradient，所有颜色必须使用rgba格式且透明度为0.3",
  "cardBackground": "卡片和内容区域背景色，使用rgba格式且透明度为0.3",
  "textColor": "主要文字颜色，确保在背景上清晰可读",
  "headingColor": "标题颜色，要醒目且与主题匹配",
  "accentColor": "强调色和装饰色",
  "borderColor": "边框和分隔线颜色",
  "icons": ["相关的emoji图标数组，5-8个，要与用户描述的主题、情感、概念相关"]
}

设计要求：
1. 根据用户情感或描述选择合适的色调（如"开心"用暖色，"忧伤"用冷色，"科技感"用蓝紫色）
2. 确保文字和背景有足够对比度，文字必须清晰可读
3. 整体配色要协调统一，形成完整的视觉体系
4. 背景必须使用渐变效果，所有颜色都要用rgba格式，透明度固定为0.3
5. 如果用户说"五彩斑斓的黑色"等创意描述，用深色为主但加入彩色元素
6. 所有背景相关的颜色都必须设置透明度为0.3

图标要求：
1. 选择与用户描述最相关的emoji
2. 例如：科技→🤖⚡💻🔧🚀，春天→🌸🌿🦋☀️🌱，海洋→🌊🐠⛵🏖️🦈
3. 图标应该能呼应用户的情感和概念
4. 数量控制在5-8个之间

请只返回JSON，不要其他文字。`
        }
      ],
      user_id: params.user_id,
      temperature: 0.0,
      max_tokens: 4000,
      stream: false
    };

    const response: IThemeGenerationResponse = await fetchInstance.fetch('/humanrelation/ai_call', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 [theme.ts] 主题生成API响应:', response);

    if (response.success && response.response.choices && response.response.choices.length > 0) {
      const content = response.response.choices[0].message.content;
      
      try {
        // 解析JSON内容
        const themeScheme: IThemeScheme = JSON.parse(content);
        console.log('✅ [theme.ts] 主题方案解析成功:', themeScheme);
        return themeScheme;
      } catch (parseError) {
        console.error('❌ [theme.ts] 主题方案JSON解析失败:', parseError, 'content:', content);
        // 返回默认主题
        return getDefaultTheme();
      }
    } else {
      console.error('❌ [theme.ts] 主题生成API响应格式异常:', response);
      // 返回默认主题
      return getDefaultTheme();
    }
  } catch (error) {
    console.error('❌ [theme.ts] 主题生成API调用失败:', error);
    // 返回默认主题
    return getDefaultTheme();
  }
}

// 默认主题方案
function getDefaultTheme(): IThemeScheme {
  return {
    background: "linear-gradient(135deg, rgba(245, 247, 250, 0.3) 0%, rgba(228, 237, 245, 0.3) 50%, rgba(216, 227, 236, 0.3) 100%)",
    cardBackground: "rgba(255, 255, 255, 0.3)",
    textColor: "#2d3748",
    headingColor: "#1a365d",
    accentColor: "#4299e1",
    borderColor: "#cbd5e0",
    icons: ["👋", "💬", "🌟", "✨", "😊", "🎯", "📝", "🚀"]
  };
}

// 应用主题到DOM元素
export function applyThemeToElement(element: HTMLElement, theme: IThemeScheme): void {
  if (!element) {
    console.warn('⚠️ [theme.ts] 应用主题失败：元素不存在');
    return;
  }

  console.log('🎨 [theme.ts] 开始应用主题到元素:', element.className, theme);

  // 应用背景
  element.style.background = theme.background;

  // 应用其他样式
  element.style.setProperty('--theme-card-background', theme.cardBackground);
  element.style.setProperty('--theme-text-color', theme.textColor);
  element.style.setProperty('--theme-heading-color', theme.headingColor);
  element.style.setProperty('--theme-accent-color', theme.accentColor);
  element.style.setProperty('--theme-border-color', theme.borderColor);

  // 添加浮动图标
  addFloatingIcons(element, theme.icons);

  console.log('✅ [theme.ts] 主题应用完成');
}

// 添加浮动图标
function addFloatingIcons(element: HTMLElement, icons: string[]): void {
  // 移除之前的浮动图标
  removeFloatingIcons(element);

  // 创建图标容器
  const iconContainer = document.createElement('div');
  iconContainer.className = 'floating-icons-container';
  iconContainer.style.cssText = `
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -10;
    overflow: hidden;
  `;

  // 检查是否是ThreeInOne页面的main-content
  const isThreeInOneMainContent = element.classList.contains('main-content') &&
    window.location.pathname.includes('three-in-one');

  // 为每个图标创建浮动元素
  icons.forEach((icon, index) => {
    const iconElement = document.createElement('div');
    iconElement.className = 'floating-icon';
    iconElement.textContent = icon;

    let randomX, randomY;

    if (isThreeInOneMainContent) {
      // ThreeInOne页面：图标分布在四周边缘，避开中间的ai-panel区域
      const side = index % 4; // 0:上, 1:右, 2:下, 3:左
      switch (side) {
        case 0: // 上边
          randomX = Math.random() * 80 + 10; // 10-90%
          randomY = Math.random() * 15 + 5; // 5-20%
          break;
        case 1: // 右边
          randomX = Math.random() * 15 + 80; // 80-95%
          randomY = Math.random() * 80 + 10; // 10-90%
          break;
        case 2: // 下边
          randomX = Math.random() * 80 + 10; // 10-90%
          randomY = Math.random() * 15 + 80; // 80-95%
          break;
        case 3: // 左边
          randomX = Math.random() * 15 + 5; // 5-20%
          randomY = Math.random() * 80 + 10; // 10-90%
          break;
      }
    } else {
      // 其他页面：正常分布
      randomX = Math.random() * 80 + 10; // 10-90%
      randomY = Math.random() * 80 + 10; // 10-90%
    }

    const randomDuration = Math.random() * 10 + 15; // 15-25秒
    const randomDelay = Math.random() * 5; // 0-5秒延迟
    const randomScale = Math.random() * 0.5 + 0.8; // 0.8-1.3倍大小

    iconElement.style.cssText = `
      position: absolute;
      left: ${randomX}%;
      top: ${randomY}%;
      font-size: ${24 * randomScale}px;
      opacity: 0.6;
      animation: float-${index} ${randomDuration}s ease-in-out infinite;
      animation-delay: ${randomDelay}s;
      transform-origin: center;
    `;

    // 创建独特的浮动动画
    const keyframes = `
      @keyframes float-${index} {
        0%, 100% {
          transform: translateY(0px) rotate(0deg) scale(${randomScale});
          opacity: 0.6;
        }
        25% {
          transform: translateY(-20px) rotate(5deg) scale(${randomScale * 1.1});
          opacity: 0.8;
        }
        50% {
          transform: translateY(-10px) rotate(-3deg) scale(${randomScale * 0.9});
          opacity: 0.4;
        }
        75% {
          transform: translateY(-15px) rotate(2deg) scale(${randomScale * 1.05});
          opacity: 0.7;
        }
      }
    `;

    // 添加动画样式
    if (!document.querySelector(`#floating-icon-style-${index}`)) {
      const style = document.createElement('style');
      style.id = `floating-icon-style-${index}`;
      style.textContent = keyframes;
      document.head.appendChild(style);
    }

    iconContainer.appendChild(iconElement);
  });

  // 确保元素有相对定位
  if (element.style.position !== 'absolute' && element.style.position !== 'fixed') {
    element.style.position = 'relative';
  }

  element.appendChild(iconContainer);
  console.log('✨ [theme.ts] 浮动图标已添加:', icons);
}

// 移除浮动图标
function removeFloatingIcons(element: HTMLElement): void {
  const existingContainer = element.querySelector('.floating-icons-container');
  if (existingContainer) {
    existingContainer.remove();
  }

  // 移除动画样式
  const styles = document.querySelectorAll('[id^="floating-icon-style-"]');
  styles.forEach(style => style.remove());
}

// 移除主题样式
export function removeThemeFromElement(element: HTMLElement): void {
  if (!element) {
    console.warn('⚠️ [theme.ts] 移除主题失败：元素不存在');
    return;
  }

  console.log('🧹 [theme.ts] 开始移除元素主题:', element.className);

  // 移除背景
  element.style.background = '';

  // 移除CSS变量
  element.style.removeProperty('--theme-card-background');
  element.style.removeProperty('--theme-text-color');
  element.style.removeProperty('--theme-heading-color');
  element.style.removeProperty('--theme-accent-color');
  element.style.removeProperty('--theme-border-color');

  // 移除浮动图标
  removeFloatingIcons(element);

  console.log('✅ [theme.ts] 主题移除完成');
}
