<template>
  <div class="three-in-one-fullscreen">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- AI比较区域 -->
      <div class="ai-comparison-section">
        <AIPanel
          ref="aiPanel1Ref"
          title="AI助手 1"
          :model-name="SUPPORTED_MODELS[1]"
          :messages="panel1Data.messages"
          :current-thinking-data="panel1Data.currentThinkingData"
          :is-loading="panel1Data.isLoading"
          :current-user-name="currentUserName"
          :current-user-id="currentUserId"
          :current-person-id="currentPersonId"
          :current-question="currentQuestion"
          :panel-index="1"
          @sub-message="(message) => handleSubMessage(message, 1)"
          @go-back="goBack"
          @add-person-memo="handleAddPersonMemo"
          @edit-person-memo="handleEditPersonMemo"
          @delete-memo="handleDeleteMemo"
          @add-industry-memo="handleAddIndustryMemo"
          @edit-industry-memo="handleEditIndustryMemo"
          @delete-industry-memo="handleDeleteIndustryMemo"
          @open-knowledge-dialog="handleOpenKnowledgeDialog"
          @add-pre-info="handleAddPreInfo"
        />
        <AIPanel
          ref="aiPanel2Ref"
          title="AI助手 2"
          :model-name="SUPPORTED_MODELS[3]"
          :messages="panel2Data.messages"
          :current-thinking-data="panel2Data.currentThinkingData"
          :is-loading="panel2Data.isLoading"
          :current-user-name="currentUserName"
          :current-user-id="currentUserId"
          :current-person-id="currentPersonId"
          :current-question="currentQuestion"
          :panel-index="2"
          @sub-message="(message) => handleSubMessage(message, 2)"
          @go-back="goBack"
          @add-person-memo="handleAddPersonMemo"
          @edit-person-memo="handleEditPersonMemo"
          @delete-memo="handleDeleteMemo"
          @add-industry-memo="handleAddIndustryMemo"
          @edit-industry-memo="handleEditIndustryMemo"
          @delete-industry-memo="handleDeleteIndustryMemo"
          @open-knowledge-dialog="handleOpenKnowledgeDialog"
          @add-pre-info="handleAddPreInfo"
        />
        <AIPanel
          ref="aiPanel3Ref"
          title="AI助手 3"
          :model-name="SUPPORTED_MODELS[4]"
          :messages="panel3Data.messages"
          :current-thinking-data="panel3Data.currentThinkingData"
          :is-loading="panel3Data.isLoading"
          :current-user-name="currentUserName"
          :current-user-id="currentUserId"
          :current-person-id="currentPersonId"
          :current-question="currentQuestion"
          :panel-index="3"
          @sub-message="(message) => handleSubMessage(message, 3)"
          @go-back="goBack"
          @add-person-memo="handleAddPersonMemo"
          @edit-person-memo="handleEditPersonMemo"
          @delete-memo="handleDeleteMemo"
          @add-industry-memo="handleAddIndustryMemo"
          @edit-industry-memo="handleEditIndustryMemo"
          @delete-industry-memo="handleDeleteIndustryMemo"
          @open-knowledge-dialog="handleOpenKnowledgeDialog"
          @add-pre-info="handleAddPreInfo"
        />
      </div>

      <!-- 输入区域 -->
      <div class="input-section">
        <InputSection
          ref="inputSectionRef"
          :has-messages="false"
          :show-submit-as-back="true"
          @send-message="handleSendMessage"
          @batch-search="handleBatchSearch"
          @go-back="goBack"
        />
      </div>
    </div>

    <!-- 备忘录对话框 -->
    <AddPersonMemo
      v-if="showAddPersonMemo && currentUserId && currentPersonId"
      :user-id="currentUserId"
      :person-id="currentPersonId"
      @close="handleClosePersonMemo"
      @save="handlePersonMemoSave"
    />

    <AddIndustryMemo
      v-if="showAddIndustryMemo && currentUserId"
      :user-id="currentUserId"
      @close="handleCloseIndustryMemo"
      @save="handleIndustryMemoSave"
    />

    <KnowledgeDialog
      v-if="showKnowledgeDialog"
      @close="handleCloseKnowledgeDialog"
    />
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { onMounted, onUnmounted, ref, onBeforeMount } from 'vue';
import { getUserInfo } from '@/apis/common';
import { getUserProfile } from '@/apis/relation';
import { comprehensiveSearchStream, SUPPORTED_MODELS } from './apis/search';
import { generateTheme, applyThemeToElement, type IThemeScheme } from './apis/theme';
import InputSection from './components/InputSection.vue';
// 备忘录组件现在在 AIPanel 中导入，这里不再需要
import AddPersonMemo from './components/AddPersonMemo.vue';
import AddIndustryMemo from './components/AddIndustryMemo.vue';
import KnowledgeDialog from './components/KnowledgeDialog.vue';
import AIPanel from './components/AIPanel.vue';

const router = useRouter();

// 输入框引用
const inputSectionRef = ref<InstanceType<typeof InputSection> | null>(null);

// AIPanel组件引用
const aiPanel1Ref = ref<InstanceType<typeof AIPanel> | null>(null);
const aiPanel2Ref = ref<InstanceType<typeof AIPanel> | null>(null);
const aiPanel3Ref = ref<InstanceType<typeof AIPanel> | null>(null);

// 用户信息
const currentUserId = ref('');
const currentPersonId = ref('');
const currentUserName = ref('');
const currentQuestion = ref(''); // 当前问题

// 备忘录相关状态现在在 AIPanel 中管理

// AIPanel数据状态
interface IThinkingItem {
  type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result' | 'core_answer_start' | 'final_answer_complete';
  message?: string;
  step?: 'start' | 'processing' | 'complete';
  question?: string;
  questions?: string[];
  results?: Array<{ title: string; link: string }>;
}

interface IThinkingData {
  items: IThinkingItem[];
  isLoading: boolean;
}

interface IMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: number;
  thinkingData?: IThinkingData; // 助手消息包含完整的思考过程
}

interface IPanelData {
  messages: IMessage[];
  isLoading: boolean;
  currentThinkingData: IThinkingData;
}

const panel1Data = ref<IPanelData>({
  messages: [],
  isLoading: false,
  currentThinkingData: { items: [], isLoading: false },
});

const panel2Data = ref<IPanelData>({
  messages: [],
  isLoading: false,
  currentThinkingData: { items: [], isLoading: false },
});

const panel3Data = ref<IPanelData>({
  messages: [],
  isLoading: false,
  currentThinkingData: { items: [], isLoading: false },
});

// 对话框状态
const showAddPersonMemo = ref(false);
const showAddIndustryMemo = ref(false);
const showKnowledgeDialog = ref(false);

// 返回上一页
const goBack = () => {
  router.back();
};

// 备忘录区域切换现在在 AIPanel 中处理

// 生成唯一ID
const generateMessageId = () => {
  return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// 生成并应用主题到 main-content
const generateAndApplyThemeToMainContent = async (message: string) => {
  try {
    console.log('🎨 [ThreeInOne] 开始生成主题到main-content:', message);

    // 调用主题生成API
    const theme = await generateTheme({
      user_id: currentUserId.value,
      user_message: message
    });

    console.log('🎨 [ThreeInOne] 主题生成成功:', theme);

    // 获取main-content元素并应用主题
    const mainContentElement = document.querySelector('.main-content') as HTMLElement;
    if (mainContentElement) {
      applyThemeToElement(mainContentElement, theme);
      console.log('✅ [ThreeInOne] 主题已应用到main-content');
    } else {
      console.warn('⚠️ [ThreeInOne] 未找到main-content元素');
    }
  } catch (error) {
    console.error('❌ [ThreeInOne] 主题生成失败:', error);
    // 主题生成失败不影响正常功能，继续执行
  }
};

// 生成并应用主题到指定的 AIPanel
const generateAndApplyThemeToPanel = async (message: string, panelIndex: number) => {
  try {
    console.log(`🎨 [ThreeInOne] 开始生成主题到面板${panelIndex}:`, message);

    // 调用主题生成API
    const theme = await generateTheme({
      user_id: currentUserId.value,
      user_message: message
    });

    console.log(`🎨 [ThreeInOne] 面板${panelIndex}主题生成成功:`, theme);

    // 获取对应的AIPanel元素并应用主题
    const panelElement = document.querySelector(`.ai-panel-wrapper:nth-child(${panelIndex}) .ai-panel`) as HTMLElement;
    if (panelElement) {
      applyThemeToElement(panelElement, theme);
      console.log(`✅ [ThreeInOne] 主题已应用到面板${panelIndex}`);
    } else {
      console.warn(`⚠️ [ThreeInOne] 未找到面板${panelIndex}元素`);
    }
  } catch (error) {
    console.error(`❌ [ThreeInOne] 面板${panelIndex}主题生成失败:`, error);
    // 主题生成失败不影响正常功能，继续执行
  }
};

// 应用批量搜索主题（从sessionStorage获取）
const applyBatchSearchTheme = () => {
  try {
    const storedTheme = sessionStorage.getItem('batchSearchTheme');
    if (storedTheme) {
      const theme = JSON.parse(storedTheme);
      console.log('🎨 [ThreeInOne] 应用批量搜索主题:', theme);

      // 获取main-content元素并应用主题
      const mainContentElement = document.querySelector('.main-content') as HTMLElement;
      if (mainContentElement) {
        applyThemeToElement(mainContentElement, theme);
        console.log('✅ [ThreeInOne] 批量搜索主题已应用到main-content');
      } else {
        console.warn('⚠️ [ThreeInOne] 未找到main-content元素');
      }

      // 清除sessionStorage中的主题数据
      sessionStorage.removeItem('batchSearchTheme');
    }
  } catch (error) {
    console.error('❌ [ThreeInOne] 应用批量搜索主题失败:', error);
  }
};

// 处理发送消息 - 按回车或点击提交按钮时触发，同时向三个AI面板发送消息
const handleSendMessage = async (message: string) => {
  if (!message.trim() || !currentUserId.value) {
    return;
  }

  console.log('🚀 [ThreeInOne] 收到消息，开始向三个AI面板发送:', message);

  // 更新当前问题
  currentQuestion.value = message;

  // 为每个面板添加用户消息
  const userMessage: IMessage = {
    id: generateMessageId(),
    type: 'user',
    content: message,
    timestamp: Date.now(),
  };

  // 添加用户消息到所有面板
  panel1Data.value.messages.push({ ...userMessage, id: generateMessageId() });
  panel2Data.value.messages.push({ ...userMessage, id: generateMessageId() });
  panel3Data.value.messages.push({ ...userMessage, id: generateMessageId() });

  // 设置加载状态
  panel1Data.value.isLoading = true;
  panel2Data.value.isLoading = true;
  panel3Data.value.isLoading = true;

  // 重置当前思考数据
  panel1Data.value.currentThinkingData = { items: [], isLoading: false };
  panel2Data.value.currentThinkingData = { items: [], isLoading: false };
  panel3Data.value.currentThinkingData = { items: [], isLoading: false };

  // 创建三个独立的搜索请求，使用不同的模型
  const searchPromises = [
    startSearchForPanel(message, panel1Data, 1, SUPPORTED_MODELS[1]), // LongCat-Flash-Chat-Preview
    startSearchForPanel(message, panel2Data, 2, SUPPORTED_MODELS[3]), // deepseek-v3-friday
    startSearchForPanel(message, panel3Data, 3, SUPPORTED_MODELS[4]), // kimi-k2-instruct-meituan
  ];

  // 并行执行主题生成和三个搜索
  try {
    await Promise.all([
      generateAndApplyThemeToMainContent(message),
      ...searchPromises
    ]);
    console.log('✅ [ThreeInOne] 所有搜索和主题生成完成');
  } catch (error) {
    console.error('❌ [ThreeInOne] 搜索或主题生成出错:', error);
  }
};

// 处理子输入框消息
const handleSubMessage = async (message: string, panelIndex: number) => {
  if (!message.trim() || !currentUserId.value) {
    return;
  }

  console.log(`🚀 [ThreeInOne] 收到面板${panelIndex}的子输入消息:`, message);

  // 更新当前问题
  currentQuestion.value = message;

  // 根据面板索引选择对应的数据
  let panelData;
  switch (panelIndex) {
    case 1:
      panelData = panel1Data;
      break;
    case 2:
      panelData = panel2Data;
      break;
    case 3:
      panelData = panel3Data;
      break;
    default:
      return;
  }

  // 如果面板正在加载中，不允许新的请求
  if (panelData.value.isLoading) {
    console.warn(`⚠️ [ThreeInOne] 面板${panelIndex}正在处理中，请等待完成`);
    return;
  }

  // 添加用户消息到对应面板
  const userMessage: IMessage = {
    id: generateMessageId(),
    type: 'user',
    content: message,
    timestamp: Date.now(),
  };

  panelData.value.messages.push(userMessage);
  panelData.value.isLoading = true;
  panelData.value.currentThinkingData = { items: [], isLoading: false };

  // 启动单个面板的搜索，根据面板索引使用对应的模型
  let modelToUse;
  switch (panelIndex) {
    case 1:
      modelToUse = SUPPORTED_MODELS[1]; // LongCat-Flash-Chat-Preview
      break;
    case 2:
      modelToUse = SUPPORTED_MODELS[3]; // deepseek-v3-friday
      break;
    case 3:
      modelToUse = SUPPORTED_MODELS[4]; // kimi-k2-instruct-meituan
      break;
    default:
      modelToUse = SUPPORTED_MODELS[1]; // 默认使用 LongCat-Flash-Chat-Preview
  }

  // 并行执行主题生成和搜索
  try {
    await Promise.all([
      generateAndApplyThemeToPanel(message, panelIndex),
      startSearchForPanel(message, panelData, panelIndex, modelToUse)
    ]);
    console.log(`✅ [ThreeInOne] 面板${panelIndex}搜索和主题生成完成`);
  } catch (error) {
    console.error(`❌ [ThreeInOne] 面板${panelIndex}搜索或主题生成出错:`, error);
  }
};

// 处理批量搜索
const handleBatchSearch = async (message: string) => {
  if (!message.trim() || !currentUserId.value) {
    return;
  }

  console.log('🚀 [ThreeInOne] 开始批量搜索:', message);

  // 更新当前问题
  currentQuestion.value = message;

  // 为每个面板添加用户消息
  const userMessage: IMessage = {
    id: generateMessageId(),
    type: 'user',
    content: message,
    timestamp: Date.now(),
  };

  // 添加用户消息到所有面板
  panel1Data.value.messages.push({ ...userMessage, id: generateMessageId() });
  panel2Data.value.messages.push({ ...userMessage, id: generateMessageId() });
  panel3Data.value.messages.push({ ...userMessage, id: generateMessageId() });

  // 设置加载状态
  panel1Data.value.isLoading = true;
  panel2Data.value.isLoading = true;
  panel3Data.value.isLoading = true;

  // 重置当前思考数据
  panel1Data.value.currentThinkingData = { items: [], isLoading: false };
  panel2Data.value.currentThinkingData = { items: [], isLoading: false };
  panel3Data.value.currentThinkingData = { items: [], isLoading: false };

  // 创建三个独立的搜索请求，使用不同的模型
  const searchPromises = [
    startSearchForPanel(message, panel1Data, 1, SUPPORTED_MODELS[1]), // LongCat-Flash-Chat-Preview
    startSearchForPanel(message, panel2Data, 2, SUPPORTED_MODELS[3]), // deepseek-v3-friday
    startSearchForPanel(message, panel3Data, 3, SUPPORTED_MODELS[4]), // kimi-k2-instruct-meituan
  ];

  // 并行执行主题生成和三个搜索
  try {
    await Promise.all([
      generateAndApplyThemeToMainContent(message),
      ...searchPromises
    ]);
    console.log('✅ [ThreeInOne] 所有批量搜索和主题生成完成');
  } catch (error) {
    console.error('❌ [ThreeInOne] 批量搜索或主题生成出错:', error);
  }
};

// 为单个面板启动搜索
const startSearchForPanel = async (message: string, panelData: any, panelIndex: number, model: string) => {
  console.log(`🔍 [ThreeInOne] 开始为面板${panelIndex}搜索:`, message, '使用模型:', model);

  // 创建AbortController用于取消请求
  const controller = new AbortController();

  try {
    await comprehensiveSearchStream(
      {
        question: message,
        user_id: currentUserId.value,
        conversation_id: `search_chat_panel_${panelIndex}`,
        model: model as any, // 使用传入的模型
      },
      {
        onStatus: (data) => {
          console.log(`📊 [ThreeInOne] 面板${panelIndex} 状态:`, data);
          const newThinkingData = {
            ...panelData.value.currentThinkingData,
            items: [
              ...panelData.value.currentThinkingData.items,
              {
                type: 'status' as const,
                message: data.message,
                step: data.step,
              }
            ]
          };
          panelData.value.currentThinkingData = newThinkingData;
        },
        onQuestion: (data) => {
          console.log(`❓ [ThreeInOne] 面板${panelIndex} 分析阶段:`, data);
          const newThinkingData = {
            ...panelData.value.currentThinkingData,
            items: [
              ...panelData.value.currentThinkingData.items,
              {
                type: 'question' as const,
                message: data.message,
              }
            ]
          };
          panelData.value.currentThinkingData = newThinkingData;
        },
        onLog: (data) => {
          console.log(`📝 [ThreeInOne] 面板${panelIndex} 日志:`, data);
          const newThinkingData = {
            ...panelData.value.currentThinkingData,
            items: [
              ...panelData.value.currentThinkingData.items,
              {
                type: 'log' as const,
                message: data.message,
              }
            ]
          };
          panelData.value.currentThinkingData = newThinkingData;
        },
        onKnowledgeContext: (data) => {
          console.log(`📚 [ThreeInOne] 面板${panelIndex} 知识上下文:`, data);
          // 可以根据需要处理知识上下文
        },
        onSearchQuestions: (data) => {
          console.log(`🔍 [ThreeInOne] 面板${panelIndex} 搜索问题:`, data);
          const newThinkingData = {
            ...panelData.value.currentThinkingData,
            items: [
              ...panelData.value.currentThinkingData.items,
              {
                type: 'search_questions' as const,
                questions: data.questions,
              }
            ]
          };
          panelData.value.currentThinkingData = newThinkingData;
        },
        onSearchResult: (data) => {
          console.log(`📋 [ThreeInOne] 面板${panelIndex} 汇总阶段:`, data);
          const newThinkingData = {
            ...panelData.value.currentThinkingData,
            items: [
              ...panelData.value.currentThinkingData.items,
              {
                type: 'search_result' as const,
                message: data.message,
              }
            ]
          };
          panelData.value.currentThinkingData = newThinkingData;
        },
        onCoreAnswerStart: (data) => {
          console.log(`🎯 [ThreeInOne] 面板${panelIndex} 核心答案开始:`, data);
          const newThinkingData = {
            ...panelData.value.currentThinkingData,
            items: [
              ...panelData.value.currentThinkingData.items,
              {
                type: 'core_answer_start' as const,
                message: '开始生成核心答案',
              }
            ]
          };
          panelData.value.currentThinkingData = newThinkingData;
        },
        onFinalAnswer: (data) => {
          console.log(`✅ [ThreeInOne] 面板${panelIndex} 最终答案:`, data);

          // 添加最终答案完成标记到思考数据
          const newThinkingData = {
            ...panelData.value.currentThinkingData,
            items: [
              ...panelData.value.currentThinkingData.items,
              {
                type: 'final_answer_complete' as const,
                message: '答案生成完成',
              }
            ],
            isLoading: false
          };
          panelData.value.currentThinkingData = newThinkingData;

          // 构建完整答案
          const coreAnswer = data.core_answer || '';
          const detailedAnswer = data.detailed_answer || '';
          const fullAnswer = [coreAnswer, detailedAnswer].filter(Boolean).join('\n\n');

          // 创建助手消息并添加到消息列表，包含完整的思考过程
          const assistantMessage: IMessage = {
            id: generateMessageId(),
            type: 'assistant',
            content: fullAnswer,
            timestamp: Date.now(),
            thinkingData: newThinkingData, // 保存完整的思考过程
          };

          panelData.value.messages.push(assistantMessage);
          panelData.value.isLoading = false;

          // 清空当前思考数据，准备下一次交互
          panelData.value.currentThinkingData = { items: [], isLoading: false };
        },
        onError: (error) => {
          console.error(`❌ [ThreeInOne] 面板${panelIndex} 搜索错误:`, error);
          panelData.value.isLoading = false;
        },
        onClose: () => {
          console.log(`🏁 [ThreeInOne] 面板${panelIndex} 搜索连接关闭`);
        },
      },
      controller.signal,
    );
  } catch (error) {
    console.error(`❌ [ThreeInOne] 面板${panelIndex} 搜索失败:`, error);
    panelData.value.isLoading = false;
  }
};

// 备忘录相关处理函数
const handleAddPersonMemo = () => {
  console.log('🔄 [ThreeInOne] 打开添加个人备忘录对话框');
  showAddPersonMemo.value = true;
};

const handleEditPersonMemo = (memo: any) => {
  console.log('🔄 [ThreeInOne] 编辑个人备忘录:', memo);
  // TODO: 实现编辑逻辑
};

const handleDeleteMemo = (memo: any) => {
  console.log('🔄 [ThreeInOne] 删除备忘录:', memo);
  // TODO: 实现删除逻辑
};

const handleAddIndustryMemo = () => {
  console.log('🔄 [ThreeInOne] 打开添加行业备忘录对话框');
  showAddIndustryMemo.value = true;
};

const handleEditIndustryMemo = (memo: any) => {
  console.log('🔄 [ThreeInOne] 编辑行业备忘录:', memo);
  // TODO: 实现编辑逻辑
};

const handleDeleteIndustryMemo = (memo: any) => {
  console.log('🔄 [ThreeInOne] 删除行业备忘录:', memo);
  // TODO: 实现删除逻辑
};

const handleOpenKnowledgeDialog = () => {
  console.log('🔄 [ThreeInOne] 打开知识库对话框');
  showKnowledgeDialog.value = true;
};

const handleAddPreInfo = () => {
  console.log('🔄 [ThreeInOne] 预获取资讯按钮被点击');
  // TODO: 实现预获取资讯功能
};

// 对话框关闭处理
const handleClosePersonMemo = () => {
  showAddPersonMemo.value = false;
};

const handleCloseIndustryMemo = () => {
  showAddIndustryMemo.value = false;
};

const handleCloseKnowledgeDialog = () => {
  showKnowledgeDialog.value = false;
};

// 备忘录保存处理
const handlePersonMemoSave = (content: string) => {
  console.log('🔄 [ThreeInOne] 个人备忘录保存成功:', content);
  showAddPersonMemo.value = false;
  // 备忘录刷新现在由 AIPanel 内部处理
};

const handleIndustryMemoSave = (content: string) => {
  console.log('🔄 [ThreeInOne] 行业备忘录保存成功:', content);
  showAddIndustryMemo.value = false;
  // 备忘录刷新现在由 AIPanel 内部处理
};

// 添加全屏样式
const addFullscreenStyles = () => {
  document.documentElement.classList.add('agentpk-active');
  document.body.classList.add('agentpk-active');
  const app = document.getElementById('app');
  if (app) {
    app.classList.add('agentpk-active');
  }
};

// 移除全屏样式
const removeFullscreenStyles = () => {
  document.documentElement.classList.remove('agentpk-active');
  document.body.classList.remove('agentpk-active');
  const app = document.getElementById('app');
  if (app) {
    app.classList.remove('agentpk-active');
  }
};

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    console.log('🔄 [ThreeInOne] 开始获取用户信息...');
    const userInfo = await getUserInfo();
    console.log('📡 [ThreeInOne] 用户信息:', userInfo);

    if (userInfo && userInfo.login) {
      currentUserId.value = userInfo.login;
      currentUserName.value = userInfo.name;
      console.log('✅ [ThreeInOne] 用户信息获取成功:', userInfo);
    } else {
      console.warn('⚠️ [ThreeInOne] 用户信息格式异常');
      currentUserId.value = 'unknown_user';
    }
  } catch (error) {
    console.error('❌ [ThreeInOne] 获取用户信息失败:', error);
    currentUserId.value = 'unknown_user';
  }
};

// 获取用户档案信息
const fetchUserProfile = async () => {
  try {
    if (!currentUserId.value) return;

    const profile = await getUserProfile({ user_id: currentUserId.value });
    if (profile.result === 'success' && profile.person) {
      currentPersonId.value = profile.person.person_id;
      console.log('✅ [ThreeInOne] 用户档案信息获取成功:', profile);

      // PersonalMemo组件会自动监听props变化并获取数据，无需手动调用
    }
  } catch (error) {
    console.error('❌ [ThreeInOne] 获取用户档案信息失败:', error);
  }
};



// 组件挂载前的准备工作
onBeforeMount(() => {
  console.log('🔄 [ThreeInOne] 页面即将挂载，添加全屏样式');
  addFullscreenStyles();
});

// 组件挂载时的初始化工作
onMounted(async () => {
  console.log('🔄 [ThreeInOne] 页面挂载，开始初始化');

  // 获取用户信息
  await fetchUserInfo();

  // 获取用户档案信息
  await fetchUserProfile();

  // 应用批量搜索主题（如果存在）
  applyBatchSearchTheme();

  // 检查是否有查询参数，如果有则自动执行搜索（不设置输入框值，避免闪烁）
  const query = router.currentRoute.value.query.q as string;
  if (query && query.trim()) {
    console.log('🔍 [ThreeInOne] 检测到查询参数，自动执行搜索:', query);

    // 直接执行批量搜索，不设置输入框值
    await handleBatchSearch(query.trim());
  }

  console.log('✅ [ThreeInOne] 页面初始化完成');
});

// 组件卸载时的清理工作
onUnmounted(() => {
  console.log('🔚 [ThreeInOne] 页面卸载，恢复原始样式');
  removeFullscreenStyles();
  console.log('✅ [ThreeInOne] 组件清理完成');
});
</script>

<style lang="scss" scoped>
.three-in-one-fullscreen {
  // 占满整个浏览器窗口，不受H5布局限制
  position: relative;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100%;
  max-width: 100%;
  background: linear-gradient(135deg, #ffffff 0%, #faf9ff 50%, #f8f6ff 100%);
  z-index: 9999;
  overflow: hidden; // 禁止整体滚动
  display: flex;
  flex-direction: column;

  // 确保不受父容器样式影响
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

// 顶部导航栏样式已移除，现在在 AIPanel 中

.main-content {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  width: 100%;
  padding: 20px 20px 120px 20px; // 顶部和底部留出空间
  flex: 1; // 占满剩余空间
  min-height: 0; // 确保flex子元素可以正确计算高度
  box-sizing: border-box;

  .ai-comparison-section {
    flex: 1;
    display: flex;
    justify-content: center;
    gap: 20px;
    width: 90vw;
    margin: 0 auto;
    padding: 0;
    min-height: calc(100vh - 200px); // 调整最小高度
    align-items: stretch;

    // 确保三个AIPanel等宽
    > * {
      flex: 1;
      min-width: 0; // 防止内容溢出
    }
  }
}

.input-section {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  padding: 20px 40px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
}
</style>

<style>
/* 全局样式，确保页面真正占满全屏 */
body.agentpk-active {
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

html.agentpk-active {
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

#app.agentpk-active {
  overflow: hidden !important;
}

/* 响应式适配 - 确保在不同屏幕尺寸下正常显示 */
@media (max-width: 768px) {
  .three-in-one-fullscreen {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 320px !important;
  }

  .main-content {
    padding: 10px 10px 120px 10px !important; /* 减少padding */
    max-width: 100% !important;
  }

  .input-section {
    padding: 20px !important;

    /* 确保输入组件在移动端正常显示 */
    :deep(.input-container) {
      max-width: 100% !important;
    }

    :deep(.input-wrapper) {
      padding: 0 16px !important;
    }
  }

  /* 备忘录区域的响应式适配 */
  .memo-section {
    gap: 8px !important;
  }

  .ai-comparison-section {
    flex-direction: column !important;
    gap: 12px !important;
    min-height: auto !important;
    width: 95vw !important;

    > * {
      flex: none !important;
      height: 400px !important; /* 增加移动端高度 */
    }
  }
}
</style>
